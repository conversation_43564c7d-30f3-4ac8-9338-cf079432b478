#!/usr/bin/env python3
"""
Test script for TextSearchAgent
"""

import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.tools.data_retriever.text_search_agent import TextSearchAgent
from models.conversation import Conversation


def test_text_search_agent():
    """Test the TextSearchAgent functionality"""
    
    # Create a temporary directory structure for testing
    test_chat_id = "test_chat_123"
    test_dir = f"storage/public/{test_chat_id}"
    
    try:
        # Create test directory
        os.makedirs(test_dir, exist_ok=True)
        
        # Create test text files
        test_file1 = f"{test_dir}/document1.txt"
        test_file2 = f"{test_dir}/document2.txt"
        
        with open(test_file1, 'w', encoding='utf-8') as f:
            f.write("""This is a sample document about artificial intelligence.
AI has revolutionized many industries including healthcare, finance, and transportation.
Machine learning algorithms are used to analyze large datasets and make predictions.
Natural language processing helps computers understand human language.""")
        
        with open(test_file2, 'w', encoding='utf-8') as f:
            f.write("""This document discusses climate change and environmental issues.
Global warming is causing rising sea levels and extreme weather events.
Renewable energy sources like solar and wind power are becoming more important.
Carbon emissions need to be reduced to mitigate climate change effects.""")
        
        # Create mock conversation object
        mock_conversation = Mock(spec=Conversation)
        mock_conversation.chat_id = test_chat_id
        
        # Create mock chat logger
        mock_chat_log = Mock()
        mock_conversation.get_chat_logger.return_value = mock_chat_log
        
        # Create TextSearchAgent instance
        agent = TextSearchAgent()
        
        # Mock the invoke_llm method to return a test response
        def mock_invoke_llm(*args, **kwargs):
            # Extract the search query from the prompt
            prompt_messages = kwargs.get('prompt_messages', [])
            if prompt_messages:
                content = prompt_messages[0]['content']
                if 'artificial intelligence' in content.lower():
                    return "Found information about artificial intelligence: AI has revolutionized many industries including healthcare, finance, and transportation. Machine learning algorithms are used to analyze large datasets and make predictions."
                elif 'climate change' in content.lower():
                    return "Found information about climate change: Global warming is causing rising sea levels and extreme weather events. Carbon emissions need to be reduced to mitigate climate change effects."
                else:
                    return "No relevant information found for the search query."
            return "No search results found."
        
        agent.invoke_llm = mock_invoke_llm
        
        # Test 1: Search for AI-related content
        print("Test 1: Searching for 'artificial intelligence'")
        result1 = agent.execute_task(
            conversation=mock_conversation,
            data_source_name="test_source",
            data_file_name_list=["document1.txt", "document2.txt"],
            task_instruction="artificial intelligence",
            base_log_path="",
            additional_data=None
        )
        
        print(f"Display Output: {result1.display_output}")
        print(f"Result Text: {result1.result_text_list}")
        print()
        
        # Test 2: Search for climate-related content
        print("Test 2: Searching for 'climate change'")
        result2 = agent.execute_task(
            conversation=mock_conversation,
            data_source_name="test_source",
            data_file_name_list=["document1.txt", "document2.txt"],
            task_instruction="climate change",
            base_log_path="",
            additional_data=None
        )
        
        print(f"Display Output: {result2.display_output}")
        print(f"Result Text: {result2.result_text_list}")
        print()
        
        # Test 3: Search with no files provided
        print("Test 3: Searching with no files")
        result3 = agent.execute_task(
            conversation=mock_conversation,
            data_source_name="test_source",
            data_file_name_list=[],
            task_instruction="test query",
            base_log_path="",
            additional_data=None
        )
        
        print(f"Display Output: {result3.display_output}")
        print(f"Result Text: {result3.result_text_list}")
        print()
        
        # Test 4: Search with non-existent file
        print("Test 4: Searching with non-existent file")
        result4 = agent.execute_task(
            conversation=mock_conversation,
            data_source_name="test_source",
            data_file_name_list=["nonexistent.txt"],
            task_instruction="test query",
            base_log_path="",
            additional_data=None
        )
        
        print(f"Display Output: {result4.display_output}")
        print(f"Result Text: {result4.result_text_list}")
        
        print("\nAll tests completed successfully!")
        
    finally:
        # Clean up test files
        if os.path.exists(test_dir):
            shutil.rmtree(os.path.dirname(test_dir))


if __name__ == "__main__":
    test_text_search_agent()
