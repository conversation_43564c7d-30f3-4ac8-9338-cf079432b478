Python code:

```python
import pandas as pd
from metalake import load_data

chat_id = "689148dcc91a0a4a3c826984"
data_files = [
    'Statement_Jul_2024_v2_1_table-1.dat',
    'Statement_Jul_2024_v2_2_table-1.dat',
    'Statement_Jul_2024_v2_3_table-1.dat',
    'Statement_Jul_2024_v2_4_table-1.dat',
    'Statement_Jul_2024_v2_5_table-1.dat',
    'Statement_Jul_2024_v2_6_table-1.dat',
    'Statement_Jul_2024_v2_7_table-1.dat',
    'Statement_Jul_2024_v2_8_table-1.dat',
    'Statement_Jul_2024_v2_9_table-1.dat',
    'Statement_Jul_2024_v2_10_table-1.dat',
    'Statement_Jul_2024_v2_11_table-1.dat',
    'Statement_Jul_2024_v2_12_table-1.dat',
    'Statement_Jul_2024_v2_13_table-1.dat',
    'Statement_Jul_2024_v2_14_table-1.dat',
]

total_files = 0
total_rows_scanned = 0
matched_rows = []

for fname in data_files:
    df = load_data(chat_id, fname)
    total_files += 1
    num_rows = len(df)
    total_rows_scanned += num_rows
    if num_rows == 0:
        continue
    # Convert all cells to string for the search (case-insensitive)
    df_str = df.astype(str)
    # Build a boolean mask for each row: does any cell contain 'BIEFENI' (case-insensitive)?
    mask = df_str.apply(lambda row: row.str.contains('BIEFENI', case=False, na=False)).any(axis=1)
    if mask.any():
        matched = df[mask].copy()
        matched['source_file'] = fname
        matched_rows.append(matched)

if matched_rows:
    combined_df = pd.concat(matched_rows, ignore_index=True)
else:
    combined_df = pd.DataFrame()  # No matches found

# Save the combined DataFrame (even if empty)
combined_df.to_csv('files/biefeni_rows.csv', index=False)

# Console output as per requirements
print(f"Number of input files processed: {total_files}")
print(f"Total rows scanned across all files: {total_rows_scanned}")
print(f"Number of rows matched: {len(combined_df)}")
if len(combined_df) > 0:
    n_display = min(5, len(combined_df))
    print(f"First {n_display} matched rows:")
    print(combined_df.head(n_display).to_markdown(index=False))
else:
    print("No rows mentioning 'BIEFENI' were found in any file.")

````

Output:

Number of input files processed: 14
Total rows scanned across all files: 273
Number of rows matched: 2
First 2 matched rows:
| 0                         | 1                  | 2   | 3        | source_file                         |
|:--------------------------|:-------------------|:----|:---------|:------------------------------------|
| New Transaction           | s forMAURO BIEFENI |     |          | Statement_Jul_2024_v2_7_table-1.dat |
| Card Number XXXX XXXXX7 8 | 1219               |     |          |                                     |
| Total of New Tra          | nsactions for      |     | 1,558.87 | Statement_Jul_2024_v2_7_table-1.dat |
| MAURO BIEFENI             |                    |     |          |                                     |

