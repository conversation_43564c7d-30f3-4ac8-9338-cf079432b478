Python code:

```python
import os
import pandas as pd
from metalake import load_data

# List of all relevant data files
DATA_FILES = [
    'Statement_Jul_2024_v2_1_table-1.dat', 'Statement_Jul_2024_v2_2_table-1.dat', 'Statement_Jul_2024_v2_3_table-1.dat',
    'Statement_Jul_2024_v2_4_table-1.dat', 'Statement_Jul_2024_v2_5_table-1.dat', 'Statement_Jul_2024_v2_6_table-1.dat',
    'Statement_Jul_2024_v2_7_table-1.dat', 'Statement_Jul_2024_v2_8_table-1.dat', 'Statement_Jul_2024_v2_9_table-1.dat',
    'Statement_Jul_2024_v2_10_table-1.dat', 'Statement_Jul_2024_v2_11_table-1.dat', 'Statement_Jul_2024_v2_12_table-1.dat',
    'Statement_Jul_2024_v2_13_table-1.dat', 'Statement_Jul_2024_v2_14_table-1.dat'
]

chat_id = "689102e6a7af24939268a106"

os.makedirs("files", exist_ok=True)

result_rows = []
columns = None
found_tables = 0

for fname in DATA_FILES:
    df = load_data(chat_id, fname)
    if df.empty:
        continue
    # Check for exact cardholder name (case-insensitive, whole cell match or substring)
    has_cardholder = df.astype(str).apply(lambda x: x.str.contains('MAURO BIEFENI', case=False, na=False)).any().any()
    if not has_cardholder:
        continue
    found_tables += 1
    col0 = df.columns[0]
    june_rows = df[df[col0].astype(str).str.contains('Jun', case=False, na=False)]
    if not june_rows.empty:
        if columns is None:
            columns = june_rows.columns
        result_rows.append(june_rows)

if result_rows:
    combined = pd.concat(result_rows, ignore_index=True)
    combined.to_csv("files/mauro_june_2024_transactions.csv", index=False)
    display_rows = min(50, len(combined))
    print(combined.head(display_rows).to_markdown(index=False))
    if len(combined) > 50:
        print(f"\nNote: Only first 50 rows shown out of {len(combined)}.")
else:
    if found_tables == 0:
        print("No data tables for card-holder 'MAURO BIEFENI' were found in the provided files.")
    else:
        print("No June 2024 transactions found for card-holder 'MAURO BIEFENI' in the provided data tables.")

````

Output:

| 0           | 1                                | 2          |      3 |
|:------------|:---------------------------------|:-----------|-------:|
| Jun26 Jun26 | ROGERS ******5052 888-7          | 64-3771    |  85.12 |
| Jun26 Jun27 | PPARKLINK RICHARDSON CE          | BURNABY    |  14    |
| Jun14 Jun15 | JAMF SOFTWARE, LLC 100           | WASHINGTON |  79.1  |
|             | UNITED STATES DOLLAR56.00@ 1.412 | 50         |        |
| Jun24 Jun24 | SPOTIFY STOCKHOLM                |            |  19.03 |
| Jun10 Jun11 | ARITZIA POLO PARK 136 WIN        | NIPEG      |  89.6  |
| Jun10 Jun11 | H&M HENNES & MAURITZ IN T        | ORONTO     | 173.71 |

