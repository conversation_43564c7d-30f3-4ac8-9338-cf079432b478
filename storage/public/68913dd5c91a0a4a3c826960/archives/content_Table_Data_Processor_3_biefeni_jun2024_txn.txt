Python code:

```python
import pandas as pd
from metalake import load_data

chat_id = "68914a85c91a0a4a3c8269cd"
data_file = "Statement_Jul_2024_v2_7_table-1.dat"

df = load_data(chat_id, data_file)
print(f"Total rows in original DataFrame: {len(df)}")

# Step 2: Build helper 'row_text' Series
row_text = df.astype(str).apply(lambda row: ' '.join(row.values), axis=1).str.lower()

# Step 3: Locate BIEFENI block start and end
start_idx = row_text[row_text.str.contains('new transaction') & row_text.str.contains('biefeni')].index
if len(start_idx) == 0:
    print("Could not find start of BIEFENI transactions block.")
    extracted = pd.DataFrame()
    idx_range = (None, None)
else:
    start_idx = start_idx[0]
    # End idx: first after start_idx with 'total of new tra' and 'biefeni'
    end_idx_candidates = row_text[(row_text.index > start_idx) & row_text.str.contains('total of new tra') & row_text.str.contains('biefeni')].index
    if len(end_idx_candidates) == 0:
        end_idx = df.index[-1] + 1  # End at last row if not found
    else:
        end_idx = end_idx_candidates[0]
    idx_range = (start_idx, end_idx)
    print(f"Index range of BIEFENI block: {idx_range}")
    df_subset = df.loc[start_idx+1:end_idx-1].copy()
    print(f"Rows in BIEFENI block: {len(df_subset)}")

    # Step 5: Filter for June rows with amount
    # Column 0 startswith 'Jun' and Column 3 is not null/blank
    col0 = df_subset.iloc[:,0].astype(str)
    col3 = df_subset.iloc[:,3].astype(str)
    mask = col0.str.startswith('Jun') & col3.str.strip().ne('')
    extracted = df_subset[mask].copy()
    print(f"Rows after June/amount filter: {len(extracted)}")

    # Step 6: Parse columns
    def split_dates(val):
        toks = str(val).split()
        return toks[0] + (' ' + toks[1] if len(toks) > 1 else '')
    # Actually, user wants both transaction and posting date, so just keep the string as is
    extracted = extracted.rename(columns={
        extracted.columns[0]: 'Transaction_Posting_Dates',
        extracted.columns[1]: 'Details_1',
        extracted.columns[2]: 'Details_2',
        extracted.columns[3]: 'Amount',
    })

# Step 7: Save to CSV
extracted.to_csv('files/mauro_biefeni_jun2024_transactions.csv', index=False)

# Step 8: Print verification and markdown
if len(idx_range) == 2:
    print(f"Index range of BIEFENI block: {idx_range}")
print(f"Rows in BIEFENI block: {len(df.loc[idx_range[0]+1:idx_range[1]-1]) if idx_range[0] is not None and idx_range[1] is not None else 0}")
print(f"Rows after June/amount filter: {len(extracted)}")

# Total amount calculation
if not extracted.empty:
    amt = extracted['Amount'].astype(str).str.replace(',', '').astype(float).sum()
    print(f"Total amount of extracted rows: ${amt:,.2f}")
    print(extracted.to_markdown(index=False))
else:
    print("No matching June 2024 transactions for MAURO BIEFENI found.")

````

Output:

Total rows in original DataFrame: 24
Index range of BIEFENI block: (np.int64(8), np.int64(14))
Rows in BIEFENI block: 5
Rows after June/amount filter: 2
Index range of BIEFENI block: (np.int64(8), np.int64(14))
Rows in BIEFENI block: 5
Rows after June/amount filter: 2
Total amount of extracted rows: $98.13
| Transaction_Posting_Dates   | Details_1                        | Details_2   |   Amount |
|:----------------------------|:---------------------------------|:------------|---------:|
| Jun14 Jun15                 | JAMF SOFTWARE, LLC 100           | WASHINGTON  |    79.1  |
|                             | UNITED STATES DOLLAR56.00@ 1.412 | 50          |          |
| Jun24 Jun24                 | SPOTIFY STOCKHOLM                |             |    19.03 |

