"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class DocumentDataQueryAgent
* @description Tool responsible for querying information from documents such as PDF, Excel, CSV and images.
* <AUTHOR>
"""

from logging import Logger
from utils.excel_file_handler import (
    convert_xls_to_xlsx,
    df_to_markdown,
    extract_csv_data,
    extract_excel_data_and_functions,
)
from utils.pdf_file_handler import extract_text_and_tables_from_pdf
from utils.metalake import save_data
from services.tools.base_tool import BaseTool, ToolOutput
from services.tools.data_processor.table_data_processor_agent import TableDataProcessorAgent
from services.tools.data_retriever.text_search_agent import TextSearchAgent
from utils.constant import AgentState, AgentToolName
from models.conversation import Conversation
from services.shared.iterative_problem_solver_agent import IterativeProblemSolverAgent
from services.tools.data_retriever.visual_data_extractor import VisualDataExtractorAgent
from services.tools.response_archive.response_data_archive import ResponseDataArchive
from utils.logger import get_debug_logger

"""
Agentic workflow extended from IterativeProblemSolverAgent for querying information from documents.
"""


class DocumentDataQueryAgent(BaseTool, IterativeProblemSolverAgent):
    def __init__(self):
        BaseTool.__init__(self)
        # Read system instruction for document data query from 'instructions/tools/document_data_retriever/system_instructions.txt'
        with open("instructions/tools/document_data_retrieve/system_instructions.txt", "r", encoding="utf-8") as file:
            system_prompt = str(file.read())
        IterativeProblemSolverAgent.__init__(self, "document_data processor", "o3", system_prompt)
        self.is_skip_attachment_scan = True  # The required file name is already provided

    def init_tools(self):
        """
        Initialize the tools dictionary with instances of all available tools for document data retrieval.
        """
        self.tools_dict: dict[str, dict] = {
            AgentToolName.TEXT_SEARCH.value: {
                "instance": TextSearchAgent(),
                "display_text": "Search text.",
            },
            AgentToolName.VISUAL_EXTRACT.value: {
                "instance": VisualDataExtractorAgent(),
                "display_text": "Extract data in visual means.",
            },
            AgentToolName.TABLE_DATA_PROCESSOR.value: {
                "instance": TableDataProcessorAgent(),
                "display_text": "Process tabular data.",
            },
            AgentToolName.MEMORY_RECALL.value: {
                "instance": ResponseDataArchive(),
                "display_text": "Recall previous results.",
            },
        }

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
    ):
        """
        Parameters:
            conversation (Conversation): The conversation object
            data_source_name (str): Document's file name
            task_instruction (str): The instruction to query the document
            base_log_path (str): Base log path
        Returns:
            ToolOutput: The output of the tool execution including both textual and tabular data

        Step1: Do textual data extraction using programmatic means based on type of the document and output a list of table and text elements
        Step2: Feed the list of elements to the agent. Based on the accuracy of extraction, agent decides which tool to use for further extraction - Pandas dataframe based processing if extraction is good enough, else text search or visual extraction
        Step3: Once the required data is retrieved, both textual and tabular data (as .dat files) is returned
        """
        conversation.agent_state = AgentState.DOCUMENT_DATA_QUERYING
        chat_log = get_debug_logger(
            f"chat_{conversation.chat_id}", f"./logs/{base_log_path}chat_{conversation.chat_id}.log"
        )
        # Extract elements from document
        all_elements = self.extract_elements_from_document(conversation, data_source_name, chat_log)
        # Start the iterative problem solving agent to query the document
        doc_metadata = {
            "file_type": data_source_name.split(".")[-1],
            "file_name": data_source_name,
            "elements_list": all_elements,
        }
        self.execute_agent(
            {"user_request": task_instruction, "document_metadata": doc_metadata},
            conversation,
            conversation.chat_id,
            chat_log,
        )

    def extract_elements_from_document(self, conversation: Conversation, file_name: str, log_handler: Logger):
        """
        Description: Extract elements(text and tables) from document using programmatic means based on type of the document
        Parameters:
            conversation (Conversation): The conversation object
            file_name (str): The name of the file to extract text from
            log_handler (Logger): The logger object
        Returns:
            list: Tabular and textual elements extracted from the document in below format:
            [
                {
                    "element_id": "text_1",    // Unique id for the element
                    "element_type": "text", // Can be "text" or "table"
                    "page_number": 1,       // Page number the element is found
                    "content_preview": "abcdef...",   //First 100 characters of the text,
                    "content_file": "file1.txt" //Text file containing full content
                    "text_size_bytes": 1524 // Length of the text in characters
                },
                {
                    "element_id": "table_1",
                    "element_type": "table",
                    "page_number": 3,   //Available only in case of PDF documents
                    "sheet_name": "Sheet1" //Available only in case of Excel documents
                    "content_preview": "<Markdown formatted table content for first 5 rows> ",
                    "data_file": "file1.dat"  // Tabular data dumped as .dat file in storage/public/{conversation.chat_id} folder
                    "row_count": 19 // Length of the table in rows
                    "additional_info": "Formulas: <list of formulas found in the table>" //Extra information such as formulas in case of Excel
                }
            ]
        """
        attachment_file_path = f"storage/public/{conversation.chat_id}/attachments/{file_name}"
        file_extension = file_name.split(".")[-1]
        supported_types = ["pdf", ".png", ".jpg", ".jpeg", ".xlsx", ".xls", ".csv"]
        # Validate file extension
        if file_extension not in supported_types:
            log_handler.warning(f"extract_elements_from_document | Unsupported file type: {file_extension}")
            return []
        # summarize_input_list: list[tuple[str, bool]] = []
        table_elements = []
        data_file_list = []
        if file_extension in ["xlsx", "xls"]:
            # in case of xls, first convert to xlsx
            if file_extension == "xls":
                attachment_file_path = convert_xls_to_xlsx(
                    attachment_file_path, f"storage/public/{conversation.chat_id}/attachments"
                )
            # Get a dictionary with sheet name as key and {"data": df, "data_dicts": data_dicts, "formulas": formulas} as value
            excel_data_dict = extract_excel_data_and_functions(attachment_file_path, 0)
            # summarize_input_list.append(
            #     (
            #         f"Extracted formulas and data from Excel file {filename}:\n\n{excel_extracted_output}\n\n",
            #         True,
            #     )
            # )
            # Add data file names to the conversation attachment file names set

            for sheet_name, sheet_data in excel_data_dict.items():
                if not sheet_data["data_dicts"]:
                    continue
                # Save data as pickle file
                data_file_name = f"{file_name.split('.')[0]}_{sheet_name}.dat"
                save_data(conversation.chat_id, data_file_name, sheet_data["data_dicts"])
                data_file_list.append(data_file_name)
                table_elements.append(
                    {
                        "element_id": f"table_{sheet_name}",
                        "element_type": "table",
                        "sheet_name": sheet_name,
                        "content_preview": df_to_markdown(sheet_data["data"].head(5)),
                        "data_file": data_file_name,
                        "row_count": len(sheet_data["data"]),
                        "additional_info": f"Formulas: {sheet_data['formulas']}",
                    }
                )
        elif file_extension == "csv":
            csv_extracted_dict = extract_csv_data(attachment_file_path, 0)
            if not csv_extracted_dict["data_dicts"]:
                return []
            # Save data as pickle file
            data_file_name = f"{file_name.split('.')[0]}.dat"
            save_data(conversation.chat_id, data_file_name, csv_extracted_dict["data_dicts"])
            data_file_list.append(data_file_name)
            table_elements.append(
                {
                    "element_id": f"table_{file_name}",
                    "element_type": "table",
                    "content_preview": df_to_markdown(csv_extracted_dict["data"].head(5)),
                    "data_file": data_file_name,
                    "row_count": len(csv_extracted_dict["data"]),
                }
            )
        elif file_extension == "pdf":
            pdf_extracted_pages = extract_text_and_tables_from_pdf(attachment_file_path)
            for page_data in pdf_extracted_pages:
                page_number = page_data["page_num"]
                # Extract text elements - merge all text segments in to one text element
                if page_data["non_table_segments"]:
                    merged_text = "\n".join(page_data["non_table_segments"])
                    text_file_name = f"{file_name.split('.')[0]}_{page_number}_text.txt"
                    with open(
                        f"storage/public/{conversation.chat_id}/{text_file_name}", "w", encoding="utf-8"
                    ) as file:
                        file.write(merged_text)
                        data_file_list.append(text_file_name)
                    table_elements.append(
                        {
                            "element_id": f"text_{page_number}",
                            "element_type": "text",
                            "page_number": page_number,
                            "content_preview": merged_text[:100],
                            "content_file": text_file_name,
                            "text_size_bytes": len(merged_text),
                        }
                    )
                # Extract table elements
                table_index = 0
                for table in page_data["tables"]:
                    table_index += 1
                    # Save data as pickle file
                    data_file_name = f"{file_name.split('.')[0]}_{page_number}_table-{table_index}.dat"
                    table_data_dict = table.to_dict("records")
                    save_data(conversation.chat_id, data_file_name, table_data_dict)
                    data_file_list.append(data_file_name)
                    table_elements.append(
                        {
                            "element_id": f"table_{page_number}_{table_index}",
                            "element_type": "table",
                            "page_number": page_number,
                            "content_preview": df_to_markdown(table.head(5)),
                            "data_file": data_file_name,
                            "row_count": len(table),
                        }
                    )

        elif file_extension in ["png", "jpg", "jpeg"]:
            # No extraction required for images
            return []
        conversation.data_reference_set.update(data_file_list)
        # # Compress the attachment data
        # attachment_data_input = self.content_archive_tool_instance.summarize_results(
        #     conversation,
        #     AgentToolName.USER_INPUT,
        #     current_cycle_index,
        #     "File uploaded by the user with question: " + user_question,
        #     summarize_input_list,
        #     "user",
        #     chat_log,
        # )
        return table_elements
