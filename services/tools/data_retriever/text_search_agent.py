"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class TextSearchAgent
* @description Tool responsible for searching a given text for a specific query.
* <AUTHOR>
"""

import os
from utils.constant import AgentState
from models.conversation import Conversation
from services.tools.base_tool import BaseTool, ToolOutput
from services.shared.base_state_handler_agent import BaseStateHandlerAgent


class TextSearchAgent(BaseTool, BaseStateHandlerAgent):
    def __init__(self):
        super().__init__("", "o3", AgentState.DOCUMENT_DATA_QUERYING)

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
    ):
        """
        Execute text search task on the provided text files.

        Args:
            conversation: The conversation object
            data_source_name: Name of the data source (not used for text files)
            data_file_name_list: List of text file names to search in
            task_instruction: The search query/instruction
            base_log_path: Base log path (optional)
            additional_data: Additional data (optional)

        Returns:
            ToolOutput: Contains search results and display output
        """
        try:
            # Get chat logger from conversation
            chat_log = conversation.get_chat_logger()

            # Check if any text files are provided
            if not data_file_name_list:
                return ToolOutput(
                    display_output="No text files provided for search.",
                    result_text_list=[("No text files provided for search.", False)],
                )

            # Read and combine text content from all provided files
            combined_text_content = ""
            processed_files = []
            missing_files = []

            for file_name in data_file_name_list:
                # Construct file path - text files are stored in storage/public/{chat_id}/
                file_path = f"storage/public/{conversation.chat_id}/{file_name}"

                # Check if file exists and is a text file
                if os.path.exists(file_path) and file_name.endswith(".txt"):
                    try:
                        with open(file_path, "r", encoding="utf-8") as file:
                            file_content = file.read()
                            combined_text_content += f"\n\n--- Content from {file_name} ---\n\n{file_content}"
                            processed_files.append(file_name)
                    except Exception as e:
                        chat_log.error(f"Error reading file {file_name}: {str(e)}")
                        missing_files.append(f"{file_name} (read error)")
                else:
                    missing_files.append(file_name)

            # Handle case where no valid text files were found
            if not combined_text_content.strip():
                error_msg = f"No valid text files found or files are empty. Missing/invalid files: {missing_files}"
                return ToolOutput(display_output=error_msg, result_text_list=[(error_msg, False)])

            # Prepare system instruction for text search
            system_instruction = """You are a text search assistant. Your task is to search through the provided text content and find information relevant to the user's query.

Instructions:
1. Carefully read through all the provided text content
2. Search for information that matches or is relevant to the user's search query
3. Extract and return the relevant portions of text that answer the query
4. If multiple relevant sections are found, include all of them
5. If no relevant information is found, clearly state that no matches were found
6. Provide context around the found information when helpful
7. Be thorough but concise in your response

Return your findings as plain text. If you find relevant information, quote the exact text and provide brief context about where it was found."""

            # Prepare user prompt with the search query and text content
            user_prompt = f"""Search Query: {task_instruction}

Text Content to Search:
{combined_text_content}

Please search through the above text content and find information relevant to the search query. Return any matching or relevant information you find."""

            # Prepare messages for LLM
            prompt_messages = [{"role": "user", "content": user_prompt}]

            # Invoke LLM to perform the search
            chat_log.info(f"TextSearchAgent: Performing text search for query: {task_instruction}")
            search_result = self.invoke_llm(
                conversation=conversation,
                system_instruction=system_instruction,
                prompt_messages=prompt_messages,
                chat_log=chat_log,
                is_current_session_history_required=False,
                follow_up_history_load_depth=0,
            )

            if not search_result:
                return ToolOutput(
                    display_output="Failed to get search results from LLM.",
                    result_text_list=[("Failed to get search results from LLM.", False)],
                )

            # Prepare display output
            files_info = f"Searched in files: {', '.join(processed_files)}"
            if missing_files:
                files_info += f"\nSkipped files: {', '.join(missing_files)}"

            display_output = f"{files_info}\n\nSearch Results:\n{search_result}"

            # Return the search results
            return ToolOutput(
                display_output=display_output, result_text_list=[(search_result, True)]  # Mark as archivable
            )

        except Exception as e:
            error_msg = f"Error during text search: {str(e)}"
            chat_log.error(error_msg) if "chat_log" in locals() else None
            return ToolOutput(display_output=error_msg, result_text_list=[(error_msg, False)])

    def on_agent_invoked(
        self,
        conversation,
        input_data,
        follow_up_history,
        current_session_history,
        chat_log,
        try_count,
        prev_invoked_count=0,
    ):
        pass

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log, try_count: int):
        pass

    def update_followup_history(self, conversation, chat_log, output_data):
        pass
