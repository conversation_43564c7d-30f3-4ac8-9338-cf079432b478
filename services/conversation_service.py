"""
* Copyright (c) 2023 ZOOMi Technologies Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* ConversationService use for handle services related to conversation

* @class ConversationService
* @description ConversationService use for handle services related to conversation
* <AUTHOR>
"""

import copy
from queue import Queue
from copy import deepcopy
from datetime import datetime, timezone, timedelta
import os
import pathlib
import shutil
import traceback
from typing import Any, Dict, List, Optional
import uuid
from fastapi import HTTPException
from dotenv import load_dotenv
import pandas as pd
import pymongo

from databases.mongo_manager import MongoDBmanager
from bson.objectid import ObjectId
from utils.misc_utils import get_data_block_visibility_list
from services.email_service import EmailService
from services.insight_app.observation_data_service import ObservationDataService
from services.global_memory_service import GlobalMemoryService

from models.conversation import Conversation
from models.models import ConversationDeleRes

from models.schemas.responses import GeneralResponse
from models.session import Session
from services.sso_user_service import SsoUserService
from utils.constant import (
    AgentState,
    ChatAppUserMessages,
    ConversationStatus,
    ConversationType,
    FrontendBlockStatus,
    FrontendBlockType,
    FrontendTabContentType,
    HypothesisReportStatus,
    HypothesisStatus,
    MessageContentType,
    SectionStatus,
    SectionType,
    LLMAgentType,
    SessionStatus,
    SessionType,
    StreamChunkOperation,
    UserReaction,
    UserType,
)
from utils.llm_utils import get_python_actions, remove_keyword_sections_from_str
from utils.logger import get_debug_logger
import zipfile
import io
import re

if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))

logger = get_debug_logger(
    "Conversation Service", pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log")
)

load_dotenv()
FRONTEND_URL = os.getenv("FRONTEND_URL")


class ConversationService:
    def __init__(self):
        self.conversation_collection = MongoDBmanager("Conversations")
        self.message_collection = MongoDBmanager("Messages")
        self.unstructured_label_mapping_collection = MongoDBmanager("UnstructuredLabelMapping")
        self.insight_dashboard_collection = MongoDBmanager("InsightDashboard")
        self.insight_report_collection = MongoDBmanager("InsightReport")
        self.hypothesis_collection = MongoDBmanager("Hypothesis")
        self.data_block_collection = MongoDBmanager("DataBlocks")
        self.sessions_collection = MongoDBmanager("Sessions")
        self.observation_data_service = ObservationDataService()
        self.sso_user_service = SsoUserService()
        self.email_service = EmailService()
        self.global_memory = GlobalMemoryService()

    def add_new_conversation(self, userId, user_name, type, is_internal=False, parent=None):
        """
        Description: Adds a new conversation to the conversation collection.

        Parameters:
            - userId: str, the user ID associated with the new conversation.
            - is_internal: Create in DB, but not show with user's converation list

        Returns:
            - dict: A dictionary indicating the success or failure of the operation.
                - isSuccess: True if the conversation was successfully added, False otherwise.
                - conversationId: The ID of the newly inserted conversation if successful.
                - message: Error message if the operation failed.

        Raises:
            - Exception: If there is an unexpected error during the document insertion.
                - message: Error message providing details about the issue.
        """

        try:
            now = datetime.now()
            total_seconds = now.hour * 3600 + now.minute * 60 + now.second
            date_time_string = now.strftime(f"%Y %b %d {total_seconds}")
            name = f"New Analysis - {date_time_string}"
            insert_obj = {
                "createdAt": datetime.now(timezone.utc),
                "updatedAt": datetime.now(timezone.utc),
                "userId": ObjectId(userId),
                "status": ConversationStatus.QUEUED.value,
                "name": name,
                "type": type,
                "userName": user_name,
                "isInternal": is_internal,
                "isViewed": False,
            }
            if parent:
                insert_obj["parentId"] = ObjectId(parent)
            res = self.conversation_collection.insert_one(insert_obj)
            if res.inserted_id:
                logger.debug(f"Successfully insert conversation")
                return {"isSuccess": True, "conversationId": str(res.inserted_id)}
            else:
                logger.error(f"Failed to get conversation id")
                return {
                    "isSuccess": False,
                    "message": ChatAppUserMessages.FAILED_TO_GET_CONVERSATION_ID.value,
                }
        except Exception as e:
            logger.error(f"add_new_conversation | Failed to insert document. Error: {e}")
            return {
                "isSuccess": False,
                "message": ChatAppUserMessages.FAILED_TO_INSERT_DOCUMENT.value,
            }

    def send_insight_report_email(self, insight_id, chat_log, shared_emails=[]):
        logger.info(f"ConversationService.send_insight_report_email | Sending email to: {shared_emails}")
        insight_report = self.insight_report_collection.get_one_document(
            {"insight_id": ObjectId(insight_id)},
            [],
            {
                "report.keyFindings": 1,
                "createdAt": 1,
                "user_id": 1,
                "email": 1,
                "_id": 1,
            },
        )
        if (
            not insight_report
            or insight_report.get("report") is None
            or insight_report.get("report", {}).get("keyFindings") is None
        ):
            chat_log.error(
                f"ConversationService.send_insight_report_email | Failed to find insight report for id: {insight_id}"
            )
            return

        logger.info(f"ConversationService.send_insight_report_email | Insight report found:")

        key_findings = insight_report["report"]["keyFindings"]

        email_data_obj = {}

        email_data_obj["shortDescription"] = key_findings.get("shortDescription", "")
        email_data_obj["mainFacts"] = [_fact["fact"] for _fact in key_findings["mainFacts"]]

        img_data = self.get_first_image(key_findings["visualizations"])
        email_data_obj["image_url"] = img_data.get("src", "")
        email_data_obj["image_caption"] = img_data.get("altText", "")
        email_data_obj["date"] = insight_report.get("createdAt", "")
        email_data_obj["year"] = datetime.now().strftime("%Y")
        email_data_obj["insight_id"] = str(insight_id)
        email_data_obj["user_id"] = str(insight_report["user_id"])

        # setting the main title
        conversation_title = self.conversation_collection.get_one_document(
            {"_id": ObjectId(insight_id)}, [], {"name": 1, "_id": 0}
        )

        email_data_obj["main_title"] = conversation_title.get("name", "")

        # check if insight dashboard record exist for this insight report id
        insight_dashboard_record = self.insight_dashboard_collection.get_one_document(
            {"insightReportId": ObjectId(insight_report["_id"])}, [], {"_id": 1, "kpiParam": 1}
        )
        if insight_dashboard_record:
            email_data_obj["url"] = (
                f"https://chat.{os.getenv('DOMAIN_URL')}/insight-board?id={str(insight_dashboard_record['_id'])}"
            )
            email_data_obj["main_title"] = insight_dashboard_record.get("kpiParam", "")
        else:
            # setting the share link
            share_res = self.share_conversation(
                conversation_id=insight_id,
                user_id=str(insight_report["user_id"]),
                user_type=UserType.USER_TYPE_SUPER_ADMIN.value,
            )
            logger.info(f"ConversationService.send_insight_report_email | Share response: {share_res}")

            email_data_obj["url"] = ""
            if share_res.success:
                logger.info(f"ConversationService.send_insight_report_email | Share response success")
                share_id = share_res.data.get("shareId", "")
                email_data_obj["url"] = f"https://chat.{os.getenv('DOMAIN_URL')}/share/3/{share_id}"

            else:
                logger.error(f"ConversationService.send_insight_report_email | Share response failed")

        formatted_html = self.email_service.insight_email_formatter(email_data_obj)

        # write this to a html file
        with open("insight_email.html", "w", encoding="utf-8") as f:
            f.write(formatted_html)

        if shared_emails:
            logger.info(f"ConversationService.send_insight_report_email | Shared emails: {shared_emails}")
            client_emails = shared_emails
        else:
            logger.info(f"ConversationService.send_insight_report_email | No shared emails")
            client_emails = list(self.email_service.to_emails or [])  # Ensure it's a list
            client_emails.append(insight_report.get("email", ""))

        logger.info(f"ConversationService.send_insight_report_email | Client emails: {client_emails}")

        # send emails one by one
        for email in client_emails:
            chat_log.info(f"ConversationService.send_insight_report_email | Sending email to: {email}")
            res = self.email_service.aws_ses_send_email(
                from_email=self.email_service.from_email,
                to_emails=[email],
                subject=f"Insight Report on {email_data_obj['main_title']}",
                plain_text_content=f"Insight Report on {email_data_obj['main_title']}",
                html_content=formatted_html,
            )
            if res["isSuccess"]:
                chat_log.info(f"ConversationService.send_insight_report_email | Email sent to: {email}")
            else:
                chat_log.warn(
                    f"ConversationService.send_insight_report_email | Failed to send email to: {email} | Error : {res['message']}"
                )

    def get_first_image(self, visualizations):
        for item in visualizations:
            for _it in item.get("items", []):
                if _it.get("type", "") == "image":
                    return {
                        "src": _it.get("src", ""),
                        "altText": _it.get("altText", ""),
                    }
        return {}

    def _add_insight_report_shared_users(self, conversation_id, shared_users_list, user_info):
        """
        Description: Update the insight report shared users.

        Parameters:
            - conversation_id: str, conversation id
            - user_ids: list, list of user ids

        Returns:
            - GeneralResponse: A response object indicating the success or failure of the operation.
                - success: True if the operation was successful, False otherwise.
                - message: Error message if the operation failed.
        """
        try:
            insight_report_obj = self.insight_report_collection.get_one_document(
                {"insight_id": ObjectId(conversation_id)}
            )
            if insight_report_obj is None:
                logger.error(
                    f"ConversationService._add_insight_report_shared_users | Insight report not found for id: {conversation_id}"
                )
                return GeneralResponse(success=False, message="Insight report not found", data={"success": False})

            existing_shared_users = insight_report_obj.get("shared_users", [])

            shared_user_ids = [ObjectId(user.id) for user in shared_users_list if user.isSelected]

            newly_added_user_ids = [user_id for user_id in shared_user_ids if user_id not in existing_shared_users]

            if not newly_added_user_ids:
                return GeneralResponse(success=True, message="No changes in shared users", data={"success": True})

            # in here I wanna get the email list of newly added users
            newly_added_user_emails = [
                user.email
                for user in shared_users_list
                if user.isSelected and ObjectId(user.id) in newly_added_user_ids
            ]

            combined_user_ids = set(existing_shared_users + newly_added_user_ids)
            combined_user_ids = list(combined_user_ids)

            if newly_added_user_ids:
                result = self._add_insight_report_shared_users_with_new_users(
                    conversation_id, combined_user_ids, user_info, newly_added_user_emails
                )

                return GeneralResponse(success=True, message="Shared users added successfully", data={"success": True})

        except Exception as e:
            logger.error(f"ConversationService._add_insight_report_shared_users | Error: {traceback.format_exc()}")
            raise e

    def _add_insight_report_shared_users_with_new_users(
        self, insight_report_id, user_ids, user_info, newly_added_user_emails
    ):
        """Update the shared_users field in the insightReport object

        Args:
            insight_report_id (ObjectId): The ID of the insight report
            user_ids (list): List of user IDs to set as shared_users,
            newly_added_user_emails (list): List of emails of newly added users
        """
        try:
            self._add_shared_users_to_conversation(insight_report_id, user_ids)
            self._add_hypothesis_reports_shared_users(insight_report_id, user_ids, user_info)
            result = self.insight_report_collection.update_one(
                {"insight_id": ObjectId(insight_report_id)},
                {"$set": {"shared_users": user_ids, "updatedAt": datetime.now(timezone.utc)}},
            )
            if result.modified_count > 0:
                logger.info(
                    f"ConversationService._add_insight_report_shared_users | add shared_users for report ID: {insight_report_id}"
                )
                base_log_path = "insight_" + insight_report_id + "/"
                log_file_full_path = f"./logs/{base_log_path}insight_{insight_report_id}.log"
                if not os.path.exists(pathlib.Path(log_file_full_path).parent):
                    os.makedirs(pathlib.Path(log_file_full_path).parent)
                chat_log = get_debug_logger(insight_report_id, log_file_full_path)
                logger.info(
                    f"ConversationService._add_insight_report_shared_users | Sending email to: {newly_added_user_emails}"
                )
                self.send_insight_report_email(insight_report_id, chat_log, newly_added_user_emails)

            else:
                logger.error(
                    f"ConversationService._add_insight_report_shared_users | Failed to update shared_users for report ID: {insight_report_id}"
                )

        except Exception as e:
            logger.error(f"ConversationService._add_insight_report_shared_users | Error: {traceback.format_exc()}")
            raise e

    def _add_hypothesis_reports_shared_users(self, insight_report_id, user_ids, user_info):
        """Update the shared_users field in the hypothesisReport object

        Args:
            insight_report_id (ObjectId): The ID of the insight report
            user_ids (list): List of user IDs to set as shared_users
        """
        try:
            hypothesis_report_objs = self.observation_data_service.get_hypothesis_list(
                insight_report_id, user_info["id"], user_info["userType"]
            )

            for hypothesis_report_obj in hypothesis_report_objs:
                self.hypothesis_collection.update_one(
                    {"_id": hypothesis_report_obj["_id"]},
                    {"$set": {"shared_users": user_ids, "updatedAt": datetime.now(timezone.utc)}},
                )
                logger.info(
                    f"ConversationService._add_hypothesis_reports_shared_users | add shared_users for hypothesis report ID: {hypothesis_report_obj['_id']}"
                )

        except Exception as e:
            logger.error(f"ConversationService._add_hypothesis_reports_shared_users | Error: {traceback.format_exc()}")
            raise e

    def _add_shared_users_to_conversation(self, conversation_id, user_ids):
        """Update the shared_users field in the conversation object

        Args:
            conversation_id (ObjectId): The ID of the conversation
            user_ids (list): List of user IDs to set as shared_users
        """
        try:
            self.conversation_collection.update_one(
                {"_id": ObjectId(conversation_id)},
                {"$set": {"shared_users": user_ids, "updatedAt": datetime.now(timezone.utc)}},
            )
            logger.info(
                f"ConversationService._add_shared_users_to_conversation | add shared_users for conversation ID: {conversation_id}"
            )

        except Exception as e:
            logger.error(f"ConversationService._add_shared_users_to_conversation | Error: {traceback.format_exc()}")
            raise e

    def add_conversation_shared_users(self, conversation_id, user_ids, user_info):
        """
        Description: Adds new users to the shared_users list of a conversation.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - user_ids: List[str], list of user IDs to add to shared_users.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - GeneralResponse: A response object indicating the success or failure of the operation.
                - success: True if the operation was successful, False otherwise.
                - message: Error message if the operation failed.
                - data: Dictionary with success status.
        """
        try:
            # Validate conversation exists and user has access
            conversation_db_obj = self.get_conversation_data(conversation_id)
            if conversation_db_obj is None:
                logger.error(
                    f"ConversationService.add_conversation_shared_users | Conversation not found for id: {conversation_id}"
                )
                return GeneralResponse(success=False, message=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)

            # User can share if they are the owner or already a shared user or super admin
            is_super_admin = user_info["userType"] == UserType.USER_TYPE_SUPER_ADMIN.value
            is_owner = str(conversation_db_obj.get("userId", "")) == str(user_info["id"])
            is_shared_user = str(user_info["id"]) in [
                str(user_id) for user_id in conversation_db_obj.get("shared_users", [])
            ]
            if not (is_super_admin or is_owner or is_shared_user):
                logger.error(
                    f"ConversationService.add_conversation_shared_users | User {user_info['id']} is not authorized to share conversation {conversation_id}"
                )
                return GeneralResponse(success=False, message=ChatAppUserMessages.NOT_CHAT_OWNER.value)

            # Convert string user IDs to ObjectId
            object_user_ids = [ObjectId(user_id) for user_id in user_ids]

            # Use $addToSet to add new user IDs to the shared_users array
            result = self.conversation_collection.update_one(
                {"_id": ObjectId(conversation_id)},
                {
                    "$addToSet": {"shared_users": {"$each": object_user_ids}},
                },
            )

            if result.modified_count > 0:
                logger.info(
                    f"ConversationService.add_conversation_shared_users | Successfully added {len(user_ids)} users to shared_users for conversation ID: {conversation_id}"
                )
                self.conversation_collection.update_one(
                    {"_id": ObjectId(conversation_id)},
                    {
                        "$set": {"updatedAt": datetime.now(timezone.utc)},
                    },
                )
                user_emails = self.get_user_emails_by_user_ids(conversation_id, user_ids, user_info)
                if user_emails:
                    log_file_full_path = f"./logs/chat_{conversation_id}.log"
                    if not os.path.exists(pathlib.Path(log_file_full_path).parent):
                        os.makedirs(pathlib.Path(log_file_full_path).parent)

                    chat_log = get_debug_logger(conversation_id, log_file_full_path)
                    self.send_last_session_answer_email(conversation_id, chat_log, user_emails, user_info)
                return GeneralResponse(success=True, message="Shared users added successfully", data={"success": True})
            else:
                logger.warning(
                    f"ConversationService.add_conversation_shared_users | No changes made to shared_users for conversation ID: {conversation_id}"
                )
                return GeneralResponse(success=True, message="No new users to add", data={"success": True})

        except Exception as e:
            logger.error(f"ConversationService.add_conversation_shared_users | Error: {traceback.format_exc()}")
            return GeneralResponse(success=False, message=str(e))

    def send_last_session_answer_email(self, conversation_id, chat_log, user_emails, user_info):
        """
        Send email to newly added users with the last session answer
        """
        try:
            user_id = user_info.get("id")
            user_type = user_info.get("userType")
            # add log about successfully come to this function
            chat_log.info(f"ConversationService.send_last_session_answer_email | Successfully come to this function")

            # get the last session answer
            conversation_obj = self.conversation_collection.get_one_document({"_id": ObjectId(conversation_id)})
            if conversation_obj is None:
                chat_log.error(
                    f"ConversationService.send_last_session_answer_email | Conversation not found for id: {conversation_id}"
                )
                return

            # get last session from session collection for given conversation id, the session types should be complex analysis of analysis
            last_session = self.sessions_collection.get_one_document(
                {
                    "conversation_id": ObjectId(conversation_id),
                    "type": {
                        "$in": [
                            SessionType.ANALYSIS.value,
                            SessionType.COMPLEX_ANALYSIS.value,
                            SessionType.DEEP_RESEARCH.value,
                        ]
                    },
                },
                sort_criteria_list=[("_id", pymongo.DESCENDING)],
            )
            if last_session is None:
                chat_log.error(
                    f"ConversationService.send_last_session_answer_email | No last session found for conversation {conversation_id}"
                )
                return

            last_session_id = str(last_session.get("_id"))

            if last_session_id:
                last_session_blocks_response = self.get_conversation_history_blocks(
                    conversation_id, user_id, user_type, last_session_id
                )
                if last_session_blocks_response.success and last_session_blocks_response.data:
                    email_data_obj = {}
                    email_data_obj = self.format_last_session_for_email(
                        last_session_blocks_response.data, chat_log, conversation_id, user_id
                    )
                    if email_data_obj:
                        formatted_html = self.email_service.last_session_answer_email_formatter(email_data_obj)
                        with open("last_session_answer_email.html", "w", encoding="utf-8") as f:
                            f.write(formatted_html)

                        for email in user_emails:
                            chat_log.info(
                                f"ConversationService.send_last_session_answer_email | Sending email to: {email}"
                            )
                            res = self.email_service.aws_ses_send_email(
                                from_email=self.email_service.from_email,
                                to_emails=[email],
                                subject=f"Analysis on {email_data_obj['main_title']}",
                                plain_text_content=f"Analysis on {email_data_obj['main_title']}",
                                html_content=formatted_html,
                            )
                            if res["isSuccess"]:
                                chat_log.info(
                                    f"ConversationService.send_last_session_answer_email | Email sent to: {email}"
                                )
                            else:
                                chat_log.warn(
                                    f"ConversationService.send_last_session_answer_email | Failed to send email to: {email} | Error : {res['message']}"
                                )

                else:
                    chat_log.error(
                        f"ConversationService.send_last_session_answer_email | Failed to get session blocks: {last_session_blocks_response.message}"
                    )

        except Exception as e:
            logger.error(f"ConversationService.send_last_session_answer_email | Error: {traceback.format_exc()}")

    def format_last_session_for_email(self, last_session_blocks, chat_log, conversation_id, user_id):
        """
        Format the answer blocks for email
        """
        try:
            email_data_obj = {}

            title = last_session_blocks.get("title", "")
            email_data_obj["main_title"] = title
            sessions = last_session_blocks.get("sessions", [])

            # share_res = self.share_conversation(
            #     conversation_id=conversation_id,
            #     user_id=str(user_id),
            #     user_type=UserType.USER_TYPE_SUPER_ADMIN.value,
            # )
            # logger.info(f"ConversationService.send_insight_report_email | Share response: {share_res}")

            email_data_obj["url"] = f"https://chat.{os.getenv('DOMAIN_URL')}/history/{conversation_id}"

            if sessions:
                last_session = sessions[-1]
                session_id = last_session.get("session_id")
                answer_blocks = last_session.get("answer", [])
                email_data_obj["answer"] = answer_blocks
                email_data_obj["year"] = datetime.now().strftime("%Y")

                filter_criteria = {"conversation_id": ObjectId(conversation_id)}
                filter_criteria["_id"] = ObjectId(session_id)
                session_info = self.sessions_collection.get_one_document(filter_criteria)

                if session_info:
                    email_data_obj["date"] = session_info.get("created_at", "")

                return email_data_obj
            else:
                chat_log.warning(
                    f"ConversationService.send_last_session_answer_email | No sessions found in conversation {conversation_id}"
                )
        except Exception as e:
            logger.error(f"ConversationService.format_answer_blocks_for_email | Error: {traceback.format_exc()}")

    def get_user_emails_by_user_ids(self, conversation_id, user_ids, user_info):
        """
        Get emails for users by fetching from SSO service

        Parameters:
            - conversation_id: str, conversation id
            - user_ids: List[str], list of user IDs
            - user_info: dict, user information containing team_id

        Returns:
            - List[str]: List of email addresses for newly added users
        """
        try:
            team_id = user_info.get("teamId")
            user_types = [UserType.USER_TYPE_AUDITOR.value, UserType.USER_TYPE_TEAM_ADMIN.value]

            user_list = self.sso_user_service.get_user_list(team_id, user_types)

            newly_added_user_emails = []
            for user in user_list:
                user_id_str = str(user.get("id"))
                if user_id_str in user_ids:
                    email = user.get("email")
                    if email:
                        newly_added_user_emails.append(email)

            logger.info(
                f"ConversationService.get_user_emails_by_user_ids | Found emails for newly added users: {newly_added_user_emails}"
            )
            return newly_added_user_emails

        except Exception as e:
            logger.error(f"ConversationService.get_user_emails_by_user_ids | Error: {traceback.format_exc()}")
            return []

    def remove_conversation_shared_users(self, conversation_id, user_ids, user_info):
        """
        Description: Removes users from the shared_users list of a conversation.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - user_ids: List[str], list of user IDs to remove from shared_users.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - GeneralResponse: A response object indicating the success or failure of the operation.
                - success: True if the operation was successful, False otherwise.
                - message: Error message if the operation failed.
                - data: Dictionary with success status.
        """
        try:
            # Validate conversation exists and user has access
            conversation_db_obj = self.get_conversation_data(conversation_id)
            if conversation_db_obj is None:
                logger.error(
                    f"ConversationService.remove_conversation_shared_users | Conversation not found for id: {conversation_id}"
                )
                return GeneralResponse(success=False, message=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)

            # User can share if they are the owner or already a shared user or super admin
            is_super_admin = user_info["userType"] == UserType.USER_TYPE_SUPER_ADMIN.value
            is_owner = str(conversation_db_obj.get("userId", "")) == str(user_info["id"])
            is_shared_user = str(user_info["id"]) in [
                str(user_id) for user_id in conversation_db_obj.get("shared_users", [])
            ]
            if not (is_super_admin or is_owner or is_shared_user):
                logger.error(
                    f"ConversationService.remove_conversation_shared_users | User {user_info['id']} is not authorized to remove users from conversation {conversation_id}"
                )
                return GeneralResponse(success=False, message=ChatAppUserMessages.NOT_CHAT_OWNER.value)

            # Convert string user IDs to ObjectId
            object_user_ids = [ObjectId(user_id) for user_id in user_ids]

            # Use $pull to remove user IDs from the shared_users array
            result = self.conversation_collection.update_one(
                {"_id": ObjectId(conversation_id)},
                {
                    "$pull": {"shared_users": {"$in": object_user_ids}},
                },
            )

            if result.modified_count > 0:
                logger.info(
                    f"ConversationService.remove_conversation_shared_users | Successfully removed {len(user_ids)} users from shared_users for conversation ID: {conversation_id}"
                )
                self.conversation_collection.update_one(
                    {"_id": ObjectId(conversation_id)},
                    {
                        "$set": {"updatedAt": datetime.now(timezone.utc)},
                    },
                )
                return GeneralResponse(
                    success=True, message="Shared users removed successfully", data={"success": True}
                )
            else:
                logger.warning(
                    f"ConversationService.remove_conversation_shared_users | No changes made to shared_users for conversation ID: {conversation_id}"
                )
                return GeneralResponse(success=True, message="No users to remove", data={"success": True})

        except Exception as e:
            logger.error(f"ConversationService.remove_conversation_shared_users | Error: {traceback.format_exc()}")
            return GeneralResponse(success=False, message=str(e))

    def get_conversation_shared_users(self, conversation_id, team_id, user_id):
        """
        Description: Get the list of all users with adding the shared status of the conversation for a given conversation ID

        Parameters:
            - conversation_id: str, conversation id
            - team_id: str, team id
            - user_id: str, user id

        Returns:
            - GeneralResponse: A response object indicating the success or failure of the operation.
                - success: True if the operation was successful, False otherwise.
                - message: Error message if the operation failed.
                - data: List of users with shared status and roles
        """

        user_types = [UserType.USER_TYPE_AUDITOR.value, UserType.USER_TYPE_TEAM_ADMIN.value]

        conversation_obj = self.conversation_collection.get_one_document({"_id": ObjectId(conversation_id)})
        if conversation_obj is None:
            logger.error(
                f"ConversationService.get_conversation_shared_users | Conversation not found for id: {conversation_id}"
            )
            return GeneralResponse(success=False, message=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)

        conversation_owner_id = str(conversation_obj.get("userId", ""))
        shared_users = [str(user_id) for user_id in conversation_obj.get("shared_users", [])]

        try:
            # Call the SSO user service to get the user list
            user_list = self.sso_user_service.get_user_list(team_id, user_types)

            # Print users for testing
            logger.info(
                f"ConversationService.get_conversation_shared_users | Retrieved {len(user_list)} users from SSO server"
            )

            formatted_user_list = []
            for user in user_list:

                _user = {
                    "id": str(user.get("id")),
                    "name": user.get("name"),
                    "email": user.get("email"),
                    "role": "editor",
                    "isSelected": False,
                }

                user_id_str = str(user.get("id"))

                # Determine user role and selection status
                if user_id_str == conversation_owner_id:
                    _user["role"] = "owner"
                    _user["isSelected"] = True  # Owner is always selected
                elif user_id_str in shared_users:
                    _user["role"] = "editor"
                    _user["isSelected"] = True
                else:
                    _user["role"] = "editor"
                    _user["isSelected"] = False

                formatted_user_list.append(_user)

            return GeneralResponse(
                success=True, message="Users retrieved successfully", data={"users": formatted_user_list}
            )

        except Exception as e:
            logger.error(f"ConversationService.get_conversation_shared_users | Error: {traceback.format_exc()}")
            return GeneralResponse(success=False, message=str(e))

    def get_insight_report_shared_users(self, conversation_id, team_id, user_id):
        """
        Description: Get the list of all users with adding the shared status of the insight report for a given conversation ID

        Parameters:
            - conversation_id: str, conversation id
            - team_id: str, team id
            - user_id: str, user id

        Returns:
            - GeneralResponse: A response object indicating the success or failure of the operation.
                - success: True if the operation was successful, False otherwise.
                - message: Error message if the operation failed.
                - data: List of users with shared status
        """

        user_types = [UserType.USER_TYPE_AUDITOR.value, UserType.USER_TYPE_TEAM_ADMIN.value]

        insight_report_obj = self.insight_report_collection.get_one_document({"insight_id": ObjectId(conversation_id)})
        if insight_report_obj is None:
            logger.error(
                f"ConversationService.get_insight_report_shared_users | Insight report not found for id: {conversation_id}"
            )
            return GeneralResponse(success=False, message="Insight report not found")

        shared_users = insight_report_obj.get("shared_users", [])

        try:
            # Call the SSO user service to get the user list
            user_list = self.sso_user_service.get_user_list(team_id, user_types)

            removed_current_user_list = [user for user in user_list if user.get("id") != user_id]

            # Print users for testing
            logger.info(
                f"ConversationService.get_insight_report_shared_users |Retrieved {len(user_list)} users from SSO server"
            )

            # Add shared status to each user
            for user in removed_current_user_list:
                logger.info(
                    f"ConversationService.get_insight_report_shared_users | User: {user.get('name')} ({user.get('email')})"
                )
                user_id_str = ObjectId(user.get("id"))
                user["isSelected"] = user_id_str in shared_users
                logger.info(
                    f"ConversationService.get_insight_report_shared_users | User: {user.get('name')} ({user.get('email')}) - isSelected: {user['isSelected']}"
                )

            return GeneralResponse(
                success=True, message="Users retrieved successfully", data={"users": removed_current_user_list}
            )

        except Exception as e:
            logger.error(f"ConversationService.get_insight_report_shared_users | Error: {traceback.format_exc()}")
            return GeneralResponse(success=False, message=str(e))

    def delete_conversation(self, conversation_id: str) -> ConversationDeleRes:
        """
        Description: Delete a conversation

        Parameters:
            - conversation_id: str, conversation id

        Returns:
            - dict: A dictionary indicating the success or failure of the operation.
                - isSuccess: True if the conversation was successfully deleted, False otherwise.

        Raises:
            - Exception: If there is an unexpected error during the delete conversation.
                - message: Error message providing details about the issue.
        """
        update_data: dict = {
            "status": ConversationStatus.DELETED.value,
            "updatedAt": datetime.now(timezone.utc),
        }
        try:
            self.conversation_collection.find_one_update({"_id": ObjectId(conversation_id)}, update_data, [])
            return {"is_success": True}
        except Exception as e:
            err_msg = f"ConversationService | ConversationService.delete_conversation | conversation_id={conversation_id} | error occur when deleting chat, {str(e)}"
            logger.error(err_msg)
            raise Exception(ChatAppUserMessages.CHAT_DELETE_ERROR.value)

    def update_is_favourite(self, conversation_id: str, is_favourite: bool) -> dict:
        """
        Description: Updates the 'isFavourite' field of a conversation.

        Parameters:
            - conversation_id: str, conversation id.
            - is_favourite: bool, the new value for the 'isFavourite' field.

        Returns:
            - dict: A dictionary indicating the success or failure of the operation.
                - is_success: True if the operation was successful, False otherwise.
        """
        update_data: dict = {
            "isFavourite": is_favourite,
        }

        try:
            # Use find_one_update to update the document
            result = self.conversation_collection.find_one_update({"_id": ObjectId(conversation_id)}, update_data, [])
            return {"is_success": True}

        except Exception as e:
            err_msg = (
                f"ConversationService | update_is_favourite | conversation_id={conversation_id} | error: {str(e)}"
            )
            logger.error(err_msg)
            raise HTTPException(status_code=500, detail="An unexpected error occurred during the update")

    def get_conversation_list(
        self,
        page_index,
        page_size,
        searchKey,
        isFavourite,
        user_info,
        date: Optional[dict],
        status,
        conversationType,
        timeZoneOffset,
    ):
        """
        Description: Retrieves a list of conversations from the conversation collection, sorted by updatedAt in descending order.

        Parameters:
            - page_index: int, the index of the page for pagination.
            - page_size: int, the size of each page for pagination.
            - searchKey: Optional[str], search key to filter conversations by name.
            - startDate: Optional[datetime], start date to filter conversations by updatedAt.
            - endDate: Optional[datetime], end date to filter conversations by updatedAt.
            - isFavourite: Optional[bool], filter conversations by favourite status.
            - user_info: dict, info about user
            - status: Optional[str], filter based on conversation status(in_progress,completed,queued,all)

        Returns:
            - list: A list of conversation documents, formatted as dictionaries.
                Each conversation dictionary contains details such as '_id' (converted to string),
                'userId' (converted to string), 'updatedAt', 'name', 'status', etc.

        Raises:
            - Exception: If there is an unexpected error during the MongoDB query aggregation.
                - message: Error message providing details about the issue.
        """
        user_id = user_info["id"]
        user_type = user_info["userType"]

        valid_statuses = [
            ConversationStatus.QUEUED.value,
            ConversationStatus.IN_PROGRESS.value,
            ConversationStatus.COMPLETED.value,
            ConversationStatus.DELETED.value,
            ConversationStatus.STOPPED.value,
            ConversationStatus.FAILED.value,
        ]

        if status == "all" or status == "":
            active_status_arr = [
                ConversationStatus.QUEUED.value,
                # ConversationStatus.IN_PROGRESS.value,   # removed in progress conversations from conversation list
                ConversationStatus.COMPLETED.value,
                ConversationStatus.STOPPED.value,
                ConversationStatus.FAILED.value,
            ]
        elif status == ConversationStatus.DELETED.value:
            active_status_arr = [ConversationStatus.DELETED.value]

        elif status in valid_statuses:
            active_status_arr = [status]

        else:
            logger.error(f"ConversationService.get_conversation_list | Invalid conversation status '{status}'")
            raise HTTPException(
                status_code=400, detail=f"Invalid status '{status}'. Valid statuses are: {valid_statuses}"
            )

        if conversationType == ConversationType.INSIGHT_GEN_CONVERSATION.value:
            conversation_type = [ConversationType.INSIGHT_GEN_CONVERSATION.value]
        elif conversationType == ConversationType.CHAT_BOT_CONVERSATION.value:
            conversation_type = [ConversationType.CHAT_BOT_CONVERSATION.value]
        else:
            conversation_type = [
                ConversationType.CHAT_BOT_CONVERSATION.value,
                ConversationType.INSIGHT_GEN_CONVERSATION.value,
            ]

        filter_criteria = {
            "status": {"$in": active_status_arr},
            "type": {"$in": conversation_type},
            "$and": [
                {"$or": [{"isInternal": {"$exists": False}}, {"isInternal": False}]},
            ],
        }

        if user_type != UserType.USER_TYPE_SUPER_ADMIN.value:
            filter_criteria["$and"].append(
                {"$or": [{"userId": ObjectId(user_id)}, {"shared_users": ObjectId(user_id)}]},
            )

        if isFavourite is True:
            filter_criteria["isFavourite"] = True

        if searchKey:
            search_words = searchKey.split()
            regex_pattern = ".*" + ".*".join(search_words) + ".*"
            filter_criteria["searchString"] = {"$regex": regex_pattern, "$options": "i"}

        if date:
            start_date = date.get("startDate")
            end_date = date.get("endDate")
            user_timezone_offset = timedelta(minutes=timeZoneOffset)
            try:
                if start_date:
                    if len(start_date) == 10:  # Format is YYYY-MM-DD
                        start_date = f"{start_date}T00:00:00"
                    start_date = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%S")
                    start_date = start_date + user_timezone_offset
                if end_date:
                    if len(end_date) == 10:  # Format is YYYY-MM-DD
                        end_date = f"{end_date}T23:59:59"
                    end_date = datetime.strptime(end_date, "%Y-%m-%dT%H:%M:%S")
                    end_date = end_date + user_timezone_offset

                if start_date and end_date:
                    filter_criteria["updatedAt"] = {"$gte": start_date, "$lte": end_date}
                elif start_date:
                    filter_criteria["updatedAt"] = {"$gte": start_date}
                elif end_date:
                    filter_criteria["updatedAt"] = {"$lte": end_date}

            except ValueError as ve:
                logger.error(f"ConversationService.get_conversation_list | Invalid date format: {ve}")
                raise HTTPException(
                    status_code=400, detail="Invalid date format. Expected 'YYYY-MM-DD' or 'YYYY-MM-DDTHH:MM:SS'."
                )

        pipeline = [
            {"$match": filter_criteria},
            {
                # Sort by updatedAt in descending order
                "$sort": {"updatedAt": -1, "_id": -1}
            },
            {"$skip": page_index * page_size},  # Skip documents based on pageIndex and pageSize
            {"$limit": page_size},  # Limit the number of documents to pageSize
            {
                "$lookup": {
                    "from": "Messages",
                    "as": "questions",
                    "let": {"conversationId": "$_id"},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {"$eq": ["$conversationId", "$$conversationId"]},
                            },
                        },
                        {"$match": {"senderType": "user", "is_front_end_data": True}},
                        {
                            "$group": {
                                "_id": None,
                                "questions": {
                                    "$push": {
                                        "$cond": {
                                            "if": {"$isArray": "$content"},
                                            "then": "Attachment",
                                            "else": "$content",
                                        }
                                    }
                                },
                            }
                        },
                    ],
                },
            },
            {
                "$project": {
                    "_id": 1,
                    "createdAt": 1,
                    "updatedAt": 1,
                    "status": 1,
                    "type": 1,
                    "name": 1,
                    "userId": 1,
                    "userName": 1,
                    "isFavourite": 1,
                    "questions": {"$arrayElemAt": ["$questions.questions", 0]},
                }
            },
        ]

        try:
            conversation_list = self.conversation_collection.aggregate(pipeline)
        except Exception as e:
            logger.error(f"ConversationService.get_conversation_list | Failed to aggregate MongoDB query. Error: {e}")
            raise HTTPException(status_code=500, detail="Failed to aggregate MongoDB query for conversation list.")

        if conversation_list is None:
            logger.warning(f"ConversationService.get_conversation_list | No conversations found")
            return []

        for conversation in conversation_list:
            conversation["_id"] = str(conversation["_id"])
            conversation["userId"] = str(conversation["userId"])

            if conversation["userId"] == str(user_id):
                if conversation["type"] == ConversationType.INSIGHT_GEN_CONVERSATION.value:
                    conversation["analysisBy"] = "@Insight generated by you"
                else:
                    conversation["analysisBy"] = "Analysis run by you"
            else:
                if conversation["type"] == ConversationType.INSIGHT_GEN_CONVERSATION.value:
                    conversation["analysisBy"] = f"@Insight generated by {conversation['userName']}"
                else:
                    conversation["analysisBy"] = f"Analysis run by {conversation['userName']}"

            # Remove "Attachment" if it's the first element in the question array and there are multiple questions
            if (
                "questions" in conversation
                and len(conversation["questions"]) > 1
                and conversation["questions"][0] == "Attachment"
            ):
                conversation["questions"].pop(0)

        logger.info(
            f"ConversationService.get_conversation_list | Successfully fetched conversations for user {user_id}"
        )
        return conversation_list

    def load_message_history(self, conversation_id, user_id):
        """
        Description: Loads the session history for a given conversation by retrieving and formatting chat messages to llm agent.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.

        Returns:
            - list: A formatted list of messages in the conversation's history.
                Each message in the list is a dictionary with relevant details.
            - If there are no messages, an empty list is returned.

        """

        # get chat history related to relevant chat id
        message_list = self.get_message_list(conversation_id, user_id)

        # format chat history
        if message_list and isinstance(message_list, list) and len(message_list) > 0:
            return self.format_message_list(message_list, False)
        else:
            return []

    def get_conversation_data(self, conversation_id) -> Optional[dict]:
        try:
            conversation_obj = self.conversation_collection.get_one_document({"_id": ObjectId(conversation_id)})
            return conversation_obj
        except Exception as e:
            logger.error(f"Failed to load conversation {conversation_id}")
            return None

    def set_conversation_type(self, conversation_id, type):
        """
        Description: update the chat type in DB into insight_agent.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.

        Raises:
            - Exception: If there is an unexpected error during the MongoDB query update.
                - message: Error message providing details about the issue.
        """
        try:
            conversation_res = self.conversation_collection.find_one_update(
                {"_id": ObjectId(conversation_id)}, {"type": type}, []
            )
        except Exception as e:
            logger.error(f"Failed to update chat type for {conversation_id} Error :{e}")
            return None

    def set_conversation_data(self, conversation_db_obj: dict, conversation: Conversation):
        conversation.data_source_types = conversation_db_obj.get("dataSources", [])
        conversation.is_file_uploaded = conversation_db_obj.get("isFileUploaded", False)
        conversation.uploaded_files = [item["objectKey"] for item in conversation_db_obj.get("uploadedFiles", [])]
        conversation.latest_uploaded_files = [
            item["objectKey"] for item in conversation_db_obj.get("uploadedFiles", []) if item["isNew"] == True
        ]
        conversation.llm_code_with_logic = conversation_db_obj.get("llmCodeWithLogic", "")

        # Add agent state history - for follow up questions
        if "agentStateHistory" in conversation_db_obj:
            # Convert the keys in agent history back to enums
            agent_history_keys_to_int = {}
            for state, state_history in conversation_db_obj["agentStateHistory"].items():
                try:
                    agent_state = AgentState(state)
                    agent_history_keys_to_int[agent_state] = {}
                    for try_count, messages in state_history.items():
                        # Filter only messages required for follow-ups
                        agent_history_keys_to_int[agent_state][int(try_count)] = []
                        for msg in messages:
                            agent_history_keys_to_int[agent_state][int(try_count)].append(
                                {
                                    "role": msg["role"],
                                    "content": msg["content"],
                                }
                            )
                except (ValueError, KeyError) as e:
                    logger.warning(f"Invalid agent state found in history: {state}. Error: {traceback.format_exc()}")
                    continue

            conversation.agent_state_follow_up_history = agent_history_keys_to_int

        # Set data reference list and first five row data
        conversation.data_reference_set = set(conversation_db_obj.get("dataReferenceList", []))
        conversation.attachment_file_names = set(conversation_db_obj.get("attachmentFileNames", []))

        # load user inputs (questions)
        _user_inputs = self.message_collection.aggregate(
            [
                {
                    "$match": {
                        "conversationId": ObjectId(conversation_db_obj["_id"]),
                        "senderType": "user",
                        "is_front_end_data": True,
                        "sectionType": SectionType.QUESTION.value,
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "user_inputs": {
                            "$push": {
                                "$cond": {
                                    "if": {"$isArray": "$content"},
                                    "then": {
                                        "$reduce": {
                                            "input": "$content",
                                            "initialValue": "",
                                            "in": {
                                                "$concat": [
                                                    "$$value",
                                                    {
                                                        "$cond": {
                                                            "if": {"$eq": ["$$value", ""]},
                                                            "else": ", ",
                                                            "then": "",
                                                        }
                                                    },
                                                    "$$this.fileKey",
                                                ]
                                            },
                                        }
                                    },
                                    "else": "$content",
                                }
                            }
                        },
                    }
                },
            ]
        )
        if isinstance(_user_inputs, list) and len(_user_inputs) > 0:
            conversation.user_inputs = _user_inputs[0].get("user_inputs", [])
        conversation.user_inputs.append(conversation.initial_question)

    def get_message_list(
        self, conversation_id, userId, userType=UserType.USER_TYPE_TEAM_ADMIN.value, is_hypothesis=False
    ):
        """
        Description: Retrieves a list of messages for a given conversation from the message collection,
                    sorted by createdAt in ascending order.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - userId: str, the unique identifier for the user.
            - userType: int, the type of user (1: super admin, 3: admin)

        Returns:
            - list: A list of message documents, formatted as dictionaries.
                Each message dictionary contains details such as 'createdAt', 'user', 'content', etc.
            - If there are no messages, an empty list is returned.

        Raises:
            - Exception: If there is an unexpected error during the MongoDB query aggregation.
                - message: Error message providing details about the issue.
        """

        # Validate input and fetch conversation or hypothesis
        if is_hypothesis:
            filter_key = "hypothesisId"
        else:
            conversation = self.get_conversation_data(conversation_id)
            if not conversation or conversation["status"] == ConversationStatus.DELETED.value:
                raise HTTPException(406, detail=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)
            filter_key = "conversationId"

        filter_value = ObjectId(conversation_id)

        pipeline = [
            {"$match": {filter_key: filter_value, "userId": ObjectId(userId)}},
            # Sort by updatedAt in descending order
            {"$sort": {"createdAt": 1}},
        ]

        if userType == UserType.USER_TYPE_SUPER_ADMIN.value:
            pipeline = [
                {"$match": {filter_key: filter_value}},
                # Sort by updatedAt in descending order
                {"$sort": {"createdAt": 1}},
            ]

        try:
            conversation_list = self.message_collection.aggregate(pipeline)
        except Exception as e:
            logger.debug(f"Failed to aggregate mongodb query for get message list. Error: {e}")
            raise Exception(ChatAppUserMessages.FAILED_TO_AGGREGATE_MONGO_QUERY_FOR_GET_MESSAGE_LIST.value)

        if conversation_list and isinstance(conversation_list, list) and len(conversation_list) > 0:
            return conversation_list
        else:
            return []

    def format_message_list(
        self,
        message_list: List[Dict[str, Any]],
        is_for_frontend=True,
        share_id=None,
        is_hypothesis=False,
        hypothesis_report_status=HypothesisReportStatus.DRAFT.value,
    ):
        """
        Description: Formats a list of messages for display, either for the frontend or backend processing.

        Parameters:
            - message_list: list, a list of message documents to be formatted.
            - is_for_frontend: bool, indicating whether the formatting is for frontend display (True) or backend processing (False).

        Returns:
            - list: A formatted list of messages. Each formatted message is a dictionary with role, content, and additional details.

        """

        _formatted_list = []

        if is_for_frontend:
            for message in message_list:
                if "is_front_end_data" in message and message["is_front_end_data"]:

                    # if share_id has given, then return only messages with given share id
                    if share_id is not None:
                        share_id_list = message.get("shareIdList", [])
                        if share_id not in share_id_list:
                            break

                    # if content_type is media, then form mediaUrl for each object in content array
                    if message["contentType"] == MessageContentType.MEDIA.value:
                        if isinstance(message["content"], list):
                            for obj in message["content"]:
                                # Type hinting for obj
                                obj: Dict[str, Any]
                                file_key = obj.get("fileKey", "")
                                media_url = obj.get("mediaUrl", "")
                                if share_id is not None:
                                    if obj.get("isMetaLakeRedirect", False):
                                        media_url = f"{FRONTEND_URL}/api/file/share/{share_id}/redirect/download?objectKey={file_key}"
                                    else:
                                        media_url = (
                                            f"{FRONTEND_URL}/api/file/share/{share_id}/download?file_path={file_key}"
                                        )
                                else:
                                    if obj.get("isMetaLakeRedirect", False):
                                        media_url = f"{FRONTEND_URL}/api/file/redirect/download?objectKey={file_key}"

                                obj["mediaUrl"] = media_url

                                if obj.get("isMetaLakeRedirect", False):
                                    obj["redirectMediaUrl"] = media_url
                                else:
                                    obj["mediaUrl"] = media_url
                    elif message["contentType"] == MessageContentType.TEXT.value:
                        # Remove special keywords contained in LLM responses text before sending to frontend
                        message["content"] = remove_keyword_sections_from_str(
                            message["content"], "SECTIONS:", "END_SECTIONS"
                        )
                        message["content"] = remove_keyword_sections_from_str(
                            message["content"], "SECTIONS\*\*:", "END_SECTIONS"
                        )
                        message["content"] = remove_keyword_sections_from_str(message["content"], "INSTRUCTIONS:")
                        message["content"] = remove_keyword_sections_from_str(message["content"], "END_INSTRUCTIONS")
                        message["content"] = remove_keyword_sections_from_str(message["content"], "FINAL:")

                    new_msg = {
                        "contents": [
                            {
                                "content": message["content"],
                                "contentType": message["contentType"],
                            }
                        ],
                        "id": str(message["conversationId"]),
                        "role": message["senderType"],
                        "createdAt": message["createdAt"].replace(tzinfo=timezone.utc),
                        "sectionType": message.get("sectionType", SectionType.DEFAULT.value),
                        "sectionStatus": (
                            SectionStatus.FAILED.value
                            if message.get("sectionStatus", SectionStatus.COMPLETED.value)
                            == SectionStatus.FAILED.value
                            else SectionStatus.COMPLETED.value
                        ),
                        "sectionId": (
                            message["sectionId"] if message.get("sectionId", None) is not None else str(uuid.uuid4())
                        ),
                        "sessionId": message["sessionId"],
                        "expanded": (
                            False
                            if message.get("sectionType", SectionType.DEFAULT.value) == SectionType.ACTION.value
                            or message.get("sectionType", SectionType.DEFAULT.value) == SectionType.QUERY_PLAN.value
                            else True
                        ),
                        "isConclusion": message.get("isConclusion", False),
                    }

                    # append first message to formatted list, it always a new section
                    if not _formatted_list:
                        _formatted_list.append(new_msg)
                    else:
                        # if section is a new section
                        if new_msg["sectionId"] != _formatted_list[-1]["sectionId"]:
                            if new_msg["isConclusion"]:
                                _formatted_list.insert(0, new_msg)
                            else:
                                _formatted_list.append(new_msg)
                        else:
                            _formatted_list[-1]["contents"].append(new_msg["contents"][0])
                            _formatted_list[-1]["sectionStatus"] = new_msg["sectionStatus"]

        else:
            for message in message_list:

                if (
                    "is_llm_agent_input" in message
                    and message["is_llm_agent_input"]
                    and "is_required_for_follow_ups" in message
                    and message["is_required_for_follow_ups"]
                ):
                    message_history = {"role": "", "content": "", "is_llm_agent_input": False, "sectionType": ""}
                    message_history["role"] = message["senderType"]
                    message_history["content"] = message["content"]
                    if "is_llm_agent_input" in message and message["is_llm_agent_input"]:
                        message_history["is_llm_agent_input"] = message["is_llm_agent_input"]
                    if "sectionType" in message and message["sectionType"]:
                        message_history["sectionType"] = message["sectionType"]
                    _formatted_list.append(message_history)

        if is_hypothesis:
            # _formatted_list = self.format_for_insight(_formatted_list, message_list[0]["conversationId"])
            new_formatted_list = []
            new_content_block = {
                "isConclusion": False,
                "id": str(message_list[0]["conversationId"]),
                "role": "assistant",
                "createdAt": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f"),
                "sectionType": "action",
                "sectionStatus": (
                    SectionStatus.COMPLETED.value
                    if hypothesis_report_status == HypothesisReportStatus.FINALIZED.value
                    or hypothesis_report_status == HypothesisReportStatus.REACT_COMPLETED.value
                    else SectionStatus.IN_PROGRESS.value
                ),
                "sectionId": str(uuid.uuid4()),
                "expanded": False,
                "contents": [],
            }
            for message in _formatted_list:
                new_content_block["contents"].extend(message.get("contents", []))
            new_formatted_list.append(new_content_block)
            return new_formatted_list

        return _formatted_list

    def update_conversation(self, conversation: Conversation, session_id, user_id):
        """
        Description: Updates conversation details based on a new session, including session history and question.

        Parameters:
            - session_id: str, the unique identifier for the current session.
            - conversation: object, Conversation instance
            - user_id: str, the user ID associated with the conversation.

        Raises:
            - Exception: If there is an unexpected error during the MongoDB update operation.
                - message: Error message providing details about the issue.

        Note:
            The function checks if there is existing session history for the conversation.
            If there is no existing history, it updates the conversation with the new session details and question.
            If there is existing history, it adds the current session to the list of sessions for the conversation.
        """
        data_update_obj = {
            "dataSources": conversation.data_source_types,
            "updatedAt": datetime.now(timezone.utc),
            "status": ConversationStatus.IN_PROGRESS.value,
            "isViewed": False,  # make conversation unread
        }
        if conversation.llm_code_with_logic:
            data_update_obj["llmCodeWithLogic"] = conversation.llm_code_with_logic

        if len(conversation.message_history) == 0:
            try:
                # add UI section to send conversation title
                # conversation.active_session.add_section(SectionType.TITLE.value)
                # conversation.active_session.add_to_queue(
                #     "Dummy Conversation Title Bla Bla Bla",
                #     "assistant",
                #     ConversationStatus.IN_PROGRESS,
                # )
                conversation.is_conversation_title_required = True
                data_update_obj["sessionIdList"] = [ObjectId(session_id)]
                self.conversation_collection.find_one_update(
                    {"_id": ObjectId(conversation.chat_id), "status": ConversationStatus.QUEUED.value},
                    data_update_obj,
                    [],
                )
            except Exception as e:
                logger.debug(f"Failed to update conversation detail. Error: {e}")
                raise Exception(ChatAppUserMessages.FAILED_TO_UPDATE_CONVERSATION.value)
        else:
            try:
                self.conversation_collection.find_one_add_to_set_and_update(
                    {
                        "_id": ObjectId(conversation.chat_id),
                    },
                    {
                        "sessionIdList": ObjectId(session_id),
                    },
                    data_update_obj,
                    [],
                )

            except Exception as e:
                logger.debug(f"Failed to update conversation detail. Error: {e}")
                raise Exception(ChatAppUserMessages.FAILED_TO_UPDATE_CONVERSATION.value)

    def share_conversation(self, conversation_id, user_id, user_type) -> GeneralResponse[dict]:

        conversation_db_obj = self.get_conversation_data(conversation_id)

        if conversation_db_obj is None:
            return GeneralResponse(success=False, message=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)

        # conversation can be shared only by owner or super admin or shared user
        is_super_admin = user_type == UserType.USER_TYPE_SUPER_ADMIN.value
        is_owner = str(conversation_db_obj.get("userId", "")) == str(user_id)
        is_shared_user = str(user_id) in [str(user_id) for user_id in conversation_db_obj.get("shared_users", [])]
        if not (is_super_admin or is_owner or is_shared_user):
            return GeneralResponse(success=False, message=ChatAppUserMessages.NOT_CHAT_OWNER.value)

        share_id = str(uuid.uuid4())

        # add share UUID to conversation
        self.conversation_collection.find_one_add_to_set(
            {"_id": ObjectId(conversation_id)},
            {"shareIdList": share_id},
        )

        # add share UUID to messages
        self.message_collection.update_many(
            {"conversationId": ObjectId(conversation_id)}, {"$push": {"shareIdList": share_id}}
        )

        return GeneralResponse(success=True, data={"shareId": share_id})

    def create_conversation_and_session(
        self,
        user_id: str,
        question,
        agent_type: LLMAgentType,
        parent_session_instance: Session = None,
        is_hide_from_end_user=False,
        parent_conversation: Conversation = None,
        is_clone_session: bool = False,
        is_copy_agent_states: bool = False,
    ):
        """
        Description: Creates a new conversation and session, and returns the conversation instance and session id.

        Parameters:
            - user_id: str, User Id.
            - question: str, the question asked by the user or instruction to action agent given by the reasoning agent.
            - is_hide_from_end_user: bool, whether to hide the conversation in history in frontend (i.e. internal conversations)
            - parent_conversation: object, parent conversation instance
            - parent_session_id: str, parent session id - this is used for creating new session for child conversation when session_instance is None
            - parent_session_queue: object, Reference to the current queue in the active session of parent conversation - this is used for creating new session for child conversation when session_instance is None
        Returns:
            - tuple: Tuple containing the conversation instance and session id.
        """
        res = self.add_new_conversation(
            userId=user_id,
            user_name="system",
            type=agent_type.value,
            is_internal=is_hide_from_end_user,
            parent=parent_conversation.chat_id if parent_conversation else None,
        )
        if res.get("isSuccess", False) == False:
            return None, None

        conversation_id = res["conversationId"]
        if is_clone_session and parent_session_instance:
            session_instance = parent_session_instance.clone_session()
            session_id = session_instance.session_id
        elif parent_session_instance:
            session_id = parent_session_instance.session_id
            session_instance = parent_session_instance
        else:
            # session_id = str(datetime.now(timezone.utc).timestamp())
            # Create session in mongodb
            session_create_res = self.initialize_session_in_db(
                conversation_id, SessionType.ANALYSIS.value, user_id, question
            )
            if not session_create_res.success:
                return None, None
            session_id = session_create_res.data
            session_instance = Session(conversation_id, session_id)

        conversation_instance = Conversation(conversation_id, session_id, question, user_id, False, agent_type)
        # We have to call this to ensure question is added to user_inputs as we dont call set_conversation_data
        conversation_instance.user_inputs.append(question)
        conversation_instance.active_session = session_instance
        self.global_memory.active_conversations[str(conversation_id)] = conversation_instance

        if parent_conversation:
            conversation_instance.parent_conversation_id = parent_conversation.chat_id
            if is_copy_agent_states:
                conversation_instance.agent_state_follow_up_history = copy.deepcopy(
                    parent_conversation.agent_state_follow_up_history
                )
                conversation_instance.is_continue_previous = True  # Works as followup question

            # Copy the data files including attachments from parent conversation to child conversation
            all_data_files_list = list(parent_conversation.data_reference_set) + list(
                parent_conversation.attachment_file_names
            )
            for data_file_name in all_data_files_list:
                if data_file_name in parent_conversation.attachment_file_names:
                    conversation_instance.attachment_file_names.add(data_file_name)
                else:
                    conversation_instance.data_reference_set.add(data_file_name)
                # Copy the data file physical file from parent conversation to child conversation
                # All .dat files are saved as .pkl files
                saved_file_name = data_file_name + ".pkl" if data_file_name.endswith(".dat") else data_file_name
                parent_data_file_path = f"storage/public/{parent_conversation.chat_id}/{saved_file_name}"
                child_data_file_path = f"storage/public/{conversation_id}/{saved_file_name}"
                if os.path.exists(parent_data_file_path):
                    # Create child data file path if not exists
                    child_data_file_dir = os.path.dirname(child_data_file_path)
                    if not os.path.exists(child_data_file_dir):
                        os.makedirs(child_data_file_dir)
                    shutil.copy(parent_data_file_path, child_data_file_path)
                else:
                    logger.warning(
                        f"conversation_service | create_conversation_and_session | Data file {parent_data_file_path} not found in parent conversation {parent_conversation.chat_id}"
                    )
            # Copy both archives and files folder from parent to child if available
            parent_conversation_path = f"storage/public/{parent_conversation.chat_id}"
            child_conversation_path = f"storage/public/{conversation_id}"
            for folder_name in ["archives", "files"]:
                parent_folder_path = f"{parent_conversation_path}/{folder_name}"
                child_folder_path = f"{child_conversation_path}/{folder_name}"
                if os.path.exists(parent_folder_path):
                    if not os.path.exists(child_folder_path):
                        os.makedirs(child_folder_path)
                    shutil.copytree(parent_folder_path, child_folder_path, dirs_exist_ok=True)

        return conversation_instance, session_id

    def copy_conversation_to_parent(
        self, child_conversation: Conversation, parent_conversation: Conversation, hypothesis_id=None
    ):
        """
        Description: Copies all messages, data files, agent states from a child conversation to its parent conversation.

        Parameters:
            - child_conversation: Conversation, the child conversation whose messages will be copied.
            - parent_conversation: Conversation, the parent conversation to which the messages will be copied.
            - hypothesis_id: str, Hypothesis Id that action belongs to

        Returns:
            - Copied no of total messages
        """
        total_messages_copied = 0

        # In case there is unstructured data label mapping associated with child conversation, set a reference for it in parent conversation
        unstructured_label_mapping = self.get_unstructured_data_label_mapping(
            child_conversation.chat_id, False, logger
        )
        if unstructured_label_mapping:
            parent_conversation.previous_unstructured_data_label_mapping_conversation_id = child_conversation.chat_id

        # Merge the agent follow up history from child conversation to parent conversation
        current_parent_try_count = len(parent_conversation.user_inputs)
        for agent_state, agent_state_history in child_conversation.agent_state_follow_up_history.items():
            if agent_state not in parent_conversation.agent_state_follow_up_history:
                parent_conversation.agent_state_follow_up_history[agent_state] = {}
            # else:
            #     current_parent_try_count = (
            #         max(parent_conversation.agent_state_follow_up_history[agent_state].keys()) + 1
            #     )
            for messages in list(agent_state_history.values()):
                if current_parent_try_count not in parent_conversation.agent_state_follow_up_history[agent_state]:
                    parent_conversation.agent_state_follow_up_history[agent_state][current_parent_try_count] = []
                for message in messages:
                    # Check if message already exists in parent conversation
                    if (
                        message
                        in parent_conversation.agent_state_follow_up_history[agent_state][current_parent_try_count]
                    ):
                        continue
                    total_messages_copied += 1
                    parent_conversation.agent_state_follow_up_history[agent_state][current_parent_try_count].append(
                        message
                    )

        # Copy the data files from child conversation to parent conversation
        for data_file_name in list(child_conversation.data_reference_set):
            parent_conversation.data_reference_set.add(data_file_name)
            # Copy the data file physical file from child conversation to parent conversation
            child_data_file_path = f"storage/public/{child_conversation.chat_id}/{data_file_name}.pkl"
            parent_data_file_path = f"storage/public/{parent_conversation.chat_id}/{data_file_name}.pkl"
            if os.path.exists(child_data_file_path):
                # Create parent data file path if not exists
                parent_data_file_dir = os.path.dirname(parent_data_file_path)
                if not os.path.exists(parent_data_file_dir):
                    os.makedirs(parent_data_file_dir)
                shutil.copy(child_data_file_path, parent_data_file_path)
            else:
                logger.warning(
                    f"conversation_service | copy_conversation_to_parent | Data file {child_data_file_path} not found in child conversation {child_conversation.chat_id}"
                )
        parent_conversation.active_session.session_answer_list = child_conversation.active_session.session_answer_list
        # TODO: check why the files list getting duplicated
        # # Append the session answers to parent
        # parent_conversation.active_session.session_answer_list.extend(
        #     child_conversation.active_session.session_answer_list
        # )

        return total_messages_copied

    def get_shared_chat(self, share_id) -> GeneralResponse[dict]:
        conversation_db_obj: dict = self.conversation_collection.get_one_document({"shareIdList": share_id})

        if conversation_db_obj is None:
            return GeneralResponse(success=False, message=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)

        all_messages = self.get_message_list(conversation_db_obj["_id"], conversation_db_obj["userId"])

        # filter by share_id and format messages
        filtered_and_formatted_messages = self.format_message_list(all_messages, share_id=share_id)

        return GeneralResponse(
            success=True,
            data={
                "messages": filtered_and_formatted_messages,
                "userName": conversation_db_obj.get("userName", ""),
                "title": conversation_db_obj.get("name", ""),
                "createdAt": conversation_db_obj.get("createdAt", ""),
                "chatId": str(conversation_db_obj.get("_id", "")),
            },
        )

    def get_shared_insight_report_v2(self, share_id: str) -> GeneralResponse[dict]:
        conversation_db_obj: dict = self.conversation_collection.get_one_document({"shareIdList": share_id})
        if conversation_db_obj is None:
            return GeneralResponse(success=False, message=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)
        insight_report = self.get_insight_report(
            conversation_db_obj["_id"], conversation_db_obj["userId"], conversation_db_obj["type"]
        )
        return GeneralResponse(
            success=True,
            data={
                "messages": insight_report,
                "userName": conversation_db_obj.get("userName", ""),
                "title": conversation_db_obj.get("name", ""),
                "createdAt": conversation_db_obj.get("createdAt", ""),
                "chatId": str(conversation_db_obj.get("_id", "")),
            },
        )

    def download_log_files(self, conversation_id):
        insight_log_folder_path = f"./logs/insight_{conversation_id}"
        insight_log_file_path = f"./logs/insight_{conversation_id}.log"
        log_file_path = f"./logs/chat_{conversation_id}.log"
        metalake_log_file_path = f"./logs/metalake_logs/chat_{conversation_id}.log"
        complex_analysis_log_file_path = f"./logs/complex_analysis_{conversation_id}.log"
        complex_log_folder_path = f"./logs/{conversation_id}"

        files_to_zip = []

        if os.path.isfile(log_file_path):
            files_to_zip.append(log_file_path)
        if os.path.isfile(metalake_log_file_path):
            files_to_zip.append(metalake_log_file_path)
        if os.path.isfile(insight_log_file_path):
            files_to_zip.append(insight_log_file_path)
        if os.path.isdir(insight_log_folder_path):
            for file in os.listdir(insight_log_folder_path):
                if file.endswith(".log"):
                    files_to_zip.append(os.path.join(insight_log_folder_path, file))

        if os.path.isfile(complex_analysis_log_file_path):
            files_to_zip.append(complex_analysis_log_file_path)
        if os.path.isdir(complex_log_folder_path):
            for file in os.listdir(complex_log_folder_path):
                if file.endswith(".log"):
                    files_to_zip.append(os.path.join(complex_log_folder_path, file))

        if not files_to_zip:
            return GeneralResponse(success=False, message=f"No log files found for conversation {conversation_id}")

        zip_file_stream = self.zip_log_files(files_to_zip=files_to_zip)

        return GeneralResponse(success=True, data={"zip_file_stream": zip_file_stream})

    def zip_log_files(self, files_to_zip):
        zip_buffer = io.BytesIO()

        try:
            with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zipper:
                for file in files_to_zip:
                    file_name = os.path.basename(file)

                    if "metalake_logs" in file:
                        arcname = f"metalake_{file_name}"
                    else:
                        arcname = file_name

                    try:
                        with open(file, "rb") as f:
                            zipper.writestr(arcname, f.read())
                    except FileNotFoundError:
                        logger.error(f"Warning: File {file} not found. Skipping.")
                    except IOError as e:
                        logger.error(f"Error: Could not read file {file}. {e}")
        except Exception as e:
            logger.error(f"Error: An error occurred while creating the ZIP file. {e}")

        zip_buffer.seek(0)
        return zip_buffer

    def format_for_insight(self, formatted_list: List[Dict[str, Any]], conversation_id: str) -> List[Dict[str, Any]]:
        """
        Formats the provided list for insight-type conversations. Adds an additional content block if the
        conversation type is an insight (type 3).

        Args:
            formatted_list (List[Dict[str, Any]]): The original list of formatted messages.
            conversation_id (str): The ID of the conversation being processed.

        Returns:
            List[Dict[str, Any]]: A modified list with an additional content block for insight-type conversations.
        """
        conversation = self.get_conversation_data(conversation_id)
        if conversation.get("type") != ConversationType.INSIGHT_GEN_CONVERSATION.value:  # Type 3 indicates insight
            return formatted_list

        updated_formatted_list = formatted_list[:1]

        # Create a new content block and this will hold rest of the messages(ReACT cycle data)
        new_content_block = {
            "isConclusion": False,
            "id": str(conversation_id),
            "role": "assistant",
            "createdAt": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f"),
            "sectionType": "action",
            "sectionStatus": "completed",
            "sectionId": str(uuid.uuid4()),
            "expanded": False,
            "contents": [],
        }

        updated_formatted_list.append(new_content_block)

        for message in formatted_list[1:]:
            for content in message.get("contents", []):
                if content["contentType"] == "text":
                    content["content"] = self.remove_csv_links(content["content"])
            new_content_block["contents"].extend(message.get("contents", []))

        return updated_formatted_list

    def remove_csv_links(self, text):
        # Regex pattern to match [SOURCE=<<...>>] format - only applicable for text, not json
        pattern = r"(\[SOURCE=.*?\]\n|<<TABLE_SOURCE=.*?>>|CSV Filepath: .*)"

        if isinstance(text, str):
            matches = re.findall(pattern, text)

            if matches:
                cleaned_text = re.sub(pattern, "", text)
                return cleaned_text
            else:
                return text
        else:
            return text

    def get_last_llm_action(self, conversation_id):
        """
        Retrieves the last LLM action from a conversation.

        Args:
            conversation_id (str): The ID of the conversation to retrieve the last LLM action from.

        Returns:
            str: The last LLM action found in the conversation, or an "INSUFFICIENT_CONTEXT" message if no action is found.
        """

        logger.debug(f"get_last_llm_action | N/A | conversation_id: {conversation_id}")

        all_llm_outputs = self.message_collection.aggregate(
            [
                {
                    "$match": {
                        "conversationId": ObjectId(conversation_id),
                        "contentType": MessageContentType.TEXT.value,
                        "senderType": "assistant",
                    }
                },
                {"$sort": {"createdAt": -1, "_id": -1}},
                {"$group": {"_id": None, "llmOutputList": {"$push": "$content"}}},
            ]
        )

        if isinstance(all_llm_outputs, list) and len(all_llm_outputs) > 0:
            all_reversed_output_list = all_llm_outputs[0].get("llmOutputList", [])

            for output in all_reversed_output_list:
                python_action = get_python_actions(output)[0]
                if python_action:
                    return python_action
            else:
                logger.debug(
                    f"get_last_llm_action | N/A | No python action found for in all llm outputs conversation_id: {conversation_id}"
                )
                return ChatAppUserMessages.INSUFFICIENT_CONTEXT.value

        else:
            logger.debug(f"get_last_llm_action | N/A | No llm outputs found for conversation_id: {conversation_id}")
            return ChatAppUserMessages.INSUFFICIENT_CONTEXT.value

    def get_last_llm_action_and_answer(self, conversation_id):

        logger.debug(f"get_last_llm_action_and_answer | N/A | conversation_id: {conversation_id}")

        pipeline = [
            {
                "$match": {"conversationId": ObjectId(conversation_id), "isChild": False}
            },  # Skip the child messages to prevent data availability check stuff
            # Sort by updatedAt in descending order
            {"$sort": {"createdAt": 1}},
        ]

        try:
            _messages_list = self.message_collection.aggregate(pipeline)

            if _messages_list and isinstance(_messages_list, list) and len(_messages_list) > 0:
                messages_list = _messages_list
            else:
                messages_list = []

            formatted_messages_list = self.format_message_list(messages_list, conversation_id)

            formatted_messages_list.reverse()

            answer_found = False
            llm_action_found = False
            llm_text_answer = ""
            llm_csv_answer = ""
            llm_action = ""
            for message in formatted_messages_list:
                if message.get("sectionType") == "action" and not llm_action_found:
                    llm_action_found = True
                    content_list = message.get("contents", [])
                    for _content in content_list:
                        if _content.get("contentType") == "text":
                            python_action = get_python_actions(_content.get("content"))[0]
                            if python_action:
                                llm_action = python_action  # replace llm_action from last python_action
                elif message.get("sectionType") == "answer" and not answer_found:
                    answer_found = True
                    content_list = message.get("contents", [])
                    for _content in content_list:
                        if _content.get("contentType") == "text":
                            llm_text_answer += _content.get("content")
                        elif _content.get("contentType") == "media":
                            media_list = _content.get("content", [])
                            for _media in media_list:
                                if _media.get("mediaType") == "csv":
                                    _file_path = f"storage/public/{conversation_id}/files/{_media.get('fileName')}"
                                    try:
                                        # read csv
                                        df = pd.read_csv(_file_path)
                                        # add it to existing csv answer as text
                                        llm_csv_answer += df.to_csv(index=False) if df is not None else ""
                                    except Exception as e:
                                        logger.error(
                                            f"get_last_llm_action_and_answer | N/A | Unexpected error during csv file read: {e}, traceback: {traceback.format_exc()}"
                                        )
                if llm_action_found and answer_found:
                    break
            return {
                "llm_action": llm_action,
                "llm_text_answer": llm_text_answer,
                "llm_csv_answer": llm_csv_answer,
                "conversation_id": conversation_id,
            }

        except Exception as e:
            logger.error(
                f"get_last_llm_action_and_answer | N/A | Unexpected error during aggregation: {e}, traceback: {traceback.format_exc()}"
            )
            return {
                "llm_action": "",
                "llm_text_answer": "",
                "llm_csv_answer": "",
                "conversation_id": conversation_id,
            }

    def get_insight_report(self, conversation_id, user_id, user_type):
        """
        Description: Retrieves the Insight report for a given conversation using aggregation pipeline.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - user_id: str, the unique identifier for the user.
            - user_type: str, the type of user.

        Returns:
            - dict: A dictionary representing the Insight report.
            - return none when there is no any messages for the given conversation
            - return only markdown data when there is no json report
        """
        pipeline = [
            {"$match": {"conversationId": ObjectId(conversation_id), "isConclusion": True}},
            {"$project": {"jsonReport": 1}},
        ]
        messages_document = self.message_collection.aggregate(pipeline)
        message_list = self.get_message_list(conversation_id, user_id, user_type)
        if message_list and isinstance(message_list, list) and len(message_list) > 0:
            markdownData = self.format_message_list(message_list)

            if messages_document and isinstance(messages_document, list) and len(messages_document) > 0:

                messages_document[0]["jsonReport"].update({"internReports": {"markdownData": markdownData}})
                return {"InsightReport": messages_document[0]["jsonReport"]}
            else:
                return {"InsightReport": {"internReports": {"markdownData": markdownData}}}
        else:
            logger.debug(f"get_insight_report | N/A | No json report found for conversation_id: {conversation_id}")
            return None

    def get_hypothesis_report(self, hypothesis_id, user_id, user_type):
        """
        Description: Retrieves the Hypothesis Report for a given hypothesis_id.

        Parameters:
            - hypothesis_id: str, the unique identifier for the hypothesis.
            - user_id: str, the unique identifier for the user.
            - user_type: str, the type of user.

        Returns:
            - dict: A dictionary representing the Hypothesis report.
            - return {} when there is an error during retrieval
        """

        formatted_hypo = {
            "hypothesis": "",
            "dataSections": [],
            "status": HypothesisReportStatus.DRAFT.value,
            "reasoning": "",
            "hypothesisStatus": HypothesisStatus.PENDING.value,
            "report": {
                "keyFindings": {
                    "shortDescription": "",
                    "mainFacts": [],
                    "visualizations": [],
                },
                "otherFacts": {
                    "bulletPoints": [],
                },
                "appendix": {
                    "visualizations": [],
                },
            },
            "reactCycles": [],
            "workLog": [],
        }

        try:

            hypo: dict = self.observation_data_service.get_hypothesis(hypothesis_id, user_id, user_type)

            if not hypo:
                logger.debug(f"get_hypothesis_report | N/A | No hypothesis found for hypothesis_id: {hypothesis_id}")
                return formatted_hypo

            # hypothesis data
            formatted_hypo["hypothesis"] = hypo.get("hypothesis", "")
            formatted_hypo["dataSections"] = hypo.get("data_sections", [])
            formatted_hypo["reasoning"] = hypo.get("reasoning", "")
            formatted_hypo["status"] = hypo.get("status", HypothesisReportStatus.DRAFT.value)

            # if LLM generated hypothesis status available, then assign it
            if formatted_hypo["status"] == HypothesisReportStatus.FINALIZED.value:
                formatted_hypo["hypothesisStatus"] = hypo.get("intern_report", {}).get(
                    "hypothesis_status", HypothesisStatus.PENDING.value
                )
            elif formatted_hypo["status"] == HypothesisReportStatus.FAILED.value:
                formatted_hypo["hypothesisStatus"] = HypothesisStatus.ERRORED.value
            else:
                formatted_hypo["hypothesisStatus"] = HypothesisStatus.PENDING.value

            # hypothesis report data
            formatted_hypo["report"]["keyFindings"]["shortDescription"] = (
                hypo.get("intern_report", {}).get("keyFindings", {}).get("shortDescription", "")
            )
            formatted_hypo["report"]["keyFindings"]["mainFacts"] = [
                fact.get("description", "")
                for fact in hypo.get("intern_report", {}).get("keyFindings", {}).get("keyFacts", [])
            ]
            formatted_hypo["report"]["keyFindings"]["visualizations"] = (
                hypo.get("intern_report", {}).get("keyFindings", {}).get("visualizations", [])
            )
            formatted_hypo["report"]["otherFacts"]["bulletPoints"] = [
                fact.get("description", "")
                for fact in hypo.get("intern_report", {}).get("otherFindings", {}).get("otherFacts", [])
            ]
            formatted_hypo["report"]["appendix"]["visualizations"] = (
                hypo.get("intern_report", {}).get("appendix", {}).get("visualizations", [])
            )

            # react cycle data
            _react_cycles = []
            for cycle_idx, cycle in enumerate(hypo.get("react_cycles", [])):

                idx = cycle.get("thought_index", cycle_idx)
                # 1st cycle contain nothing, it is the initializer
                if idx == 0:
                    continue

                _cycle = {
                    "id": idx,
                    "name": f"Thought {idx}",
                    "thought": "",
                    "thought": cycle.get("thought_reasoning", ""),
                    "objective": cycle.get("analysis_objective", ""),
                    "code": "",
                    "data": [],
                    "insight": "",
                    "isDataAvailable": False,
                    "isCodeAvailable": False,
                }

                _cycle["reasoning"] = cycle.get("thought_instruction", "")  # TODO: Rename this to "instruction"
                _cycle["code"] = cycle.get("code", "")
                _cycle["data"] = cycle.get("visualizations", [])

                # truncate the tables where data rows are more than 100
                for _visualization in _cycle["data"]:
                    if _visualization.get("type", "") == "table" and _visualization.get("isTruncated", False):
                        _visualization["data"]["rows"] = _visualization.get("data", {}).get("rows", [])[:100]

                _cycle["insight"] = cycle.get("insight", "")
                _cycle["isDataAvailable"] = True if len(cycle.get("visualizations", [])) > 0 else False
                _cycle["isCodeAvailable"] = True if len(cycle.get("code", "")) > 0 else False

                _react_cycles.append(_cycle)

            formatted_hypo["reactCycles"] = _react_cycles

            # assign conversation log
            message_list = self.get_message_list(hypothesis_id, user_id, user_type, True)
            if message_list and isinstance(message_list, list) and len(message_list) > 0:
                formatted_message_list = self.format_message_list(
                    message_list, is_hypothesis=True, hypothesis_report_status=formatted_hypo["status"]
                )
                formatted_hypo["workLog"] = formatted_message_list

        except Exception as e:
            logger.error(
                f"get_hypothesis_report | Error while getting hypothesis report: {traceback.format_exc()}, hypothesis_id: {hypothesis_id}"
            )

        return formatted_hypo

    def load_conversation(self, conversation_id, user_id=None):
        """
        Description: Load a conversation data from DB and files and then create Conversation Instance

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.

        Returns:
            - dict: A dictionary representing the conversation instance.
        """
        conversation_db_obj = self.get_conversation_data(conversation_id)

        if not conversation_db_obj:
            logger.error(f"load_conversation | N/A | No conversation found for conversation_id: {conversation_id}")
            return None

        if user_id is None:
            user_id = conversation_db_obj.get("userId", "")
        user_question = conversation_db_obj.get("userQuestion", "")
        conversation_type: ConversationType = conversation_db_obj.get("type", 1)
        agent_type = LLMAgentType(conversation_db_obj.get("agent_type", LLMAgentType.AGENT_TYPE_CHAT.value))
        if conversation_type == ConversationType.INSIGHT_GEN_CONVERSATION.value:
            agent_type = LLMAgentType.AGENT_TYPE_INSIGHT

        # Get the latest session belonging to this conversation from DB
        session_db_obj = self.sessions_collection.get_one_document(
            {"conversation_id": ObjectId(conversation_id)}, sort_criteria_list=[("_id", pymongo.DESCENDING)]
        )
        if session_db_obj is None:
            logger.error(f"load_conversation | N/A | No session found for conversation_id: {conversation_id}")
            # create new session
            session_id = str(datetime.now(timezone.utc).timestamp())
            session_init = self.initialize_session_in_db(conversation_id, session_id, user_id, "")
            if session_init.success:
                session_id = session_init.data
            else:
                logger.error(
                    f"load_conversation | N/A | Failed to initialize session for conversation_id: {conversation_id}"
                )

        else:
            session_id = str(session_db_obj.get("_id"))

        # # create new session
        # session_id = str(datetime.now(timezone.utc).timestamp())
        session_instance = Session(conversation_id, session_id)

        # get session history related to conversation
        history_messages = self.load_message_history(conversation_id, user_id)

        conversation_instance = Conversation(
            conversation_id, session_id, user_question, user_id, len(history_messages) > 0, agent_type
        )

        self.set_conversation_data(conversation_db_obj, conversation_instance)
        conversation_instance.active_session = session_instance
        conversation_instance.message_history = history_messages

        # Set the file names for the conversation from folder "storage/public/{conversation_id}/files" - csv file as data files and png as images
        folder_path = pathlib.Path.joinpath(
            pathlib.Path(__file__).parent.resolve(), f"../storage/public/{conversation_id}/files"
        )
        if folder_path.exists():
            conversation_instance.insight_data_file_paths = [f.name for f in folder_path.glob("*.csv")]
            conversation_instance.insight_image_file_paths = [f.name for f in folder_path.glob("*.png")]
            conversation_instance.initial_insight_data_file_names_before_current_session = [
                f.name for f in folder_path.glob("*.csv")
            ]
            conversation_instance.initial_insight_image_file_names_before_current_session = [
                f.name for f in folder_path.glob("*.png")
            ]

        logger.debug(
            f"load_conversation | N/A | Conversation loaded for conversation_id: {conversation_id}, input image files: {conversation_instance.insight_image_file_paths}, input data files: {conversation_instance.insight_data_file_paths}"
        )
        return conversation_instance

    def get_hypothesis_list(self, insight_id, user_id, user_type):
        """
        Retrieves a list of hypotheses for a given insight.

        Parameters:
            - insight_id: str, the unique identifier for the insight.
            - user_id: str, the unique identifier for the user.
            - user_type: str, the type of user.

        Returns:
            - list: A list of dictionaries representing the hypotheses for the given insight.
        """
        hypo_list = self.observation_data_service.get_hypothesis_list(insight_id, user_id, user_type)
        return hypo_list

    def stop_insight(self, insight_id):
        """
        Stop the insight generation process.

        Parameters:
            - insight_id: str, the unique identifier for the insight.
        """
        return self.observation_data_service.stop_insight(insight_id)

    def set_unstructured_data_label_mapping(self, chat_id, labelling_task, label_mapping):
        """
        Stores the label mapping for a given conversation for unstructured data labelling.

        Parameters:
            - chat_id: str, the unique identifier for the conversation.
            - labelling_task: str, the labelling task
            - label_mapping: dict, the label mapping in the format of {"<label_name>": ["<matching_labels>"]}
        """
        self.unstructured_label_mapping_collection.update_one(
            {"conversationId": ObjectId(chat_id)},
            {"$set": {"labellingTask": labelling_task, "labelMapping": label_mapping}},
            upsert=True,
        )

    def get_unstructured_data_label_mapping(self, chat_id, is_check_previous, chat_log):
        """
        Retrieves the label mapping for a given conversation for unstructured data labelling.

        Parameters:
            - chat_id: str, the unique identifier for the conversation.
            - is_check_previous: bool, whether to check for the previous conversation.

        Returns:
            - dict: The label mapping in the format of {"<label_name>": ["<matching_labels>"]}
        """
        unstructured_label_mapping = self.unstructured_label_mapping_collection.get_one_document(
            {"conversationId": ObjectId(chat_id)}
        )
        # In case of insight REACT cycle, if no label mapping associated with this conversation, then see if there is for the previous action agent
        if unstructured_label_mapping:
            chat_log.debug(f"get_unstructured_data_label_mapping: Found label mapping for conversation id: {chat_id}")
        elif is_check_previous:
            # Get conversation id for prev. label mapping if exits
            conversation_obj = self.conversation_collection.get_one_document(
                {"_id": ObjectId(chat_id)}, [], ["previousUnstructuredDataLabelMappingConversationId"]
            )
            if conversation_obj and conversation_obj.get("previousUnstructuredDataLabelMappingConversationId"):
                chat_log.debug(
                    f"get_unstructured_data_label_mapping: Conversation id: {chat_id}, Previous conversation id: {str(conversation_obj.get('previousUnstructuredDataLabelMappingConversationId'))}"
                )
                unstructured_label_mapping = self.unstructured_label_mapping_collection.get_one_document(
                    {"conversationId": conversation_obj.get("previousUnstructuredDataLabelMappingConversationId")}
                )
                if unstructured_label_mapping:
                    chat_log.debug(
                        f"get_unstructured_data_label_mapping: Found previous label mapping for conversation id: {chat_id}"
                    )
                else:
                    chat_log.debug(
                        f"get_unstructured_data_label_mapping: Previous label mapping not found for conversation id: {chat_id}"
                    )
            else:
                chat_log.debug(
                    f"get_unstructured_data_label_mapping: Conversation id: {chat_id}, Previous conversation id not available"
                )

        return unstructured_label_mapping

    def set_previous_unstructured_data_label_mapping_conversation_id(self, chat_id, previous_conversation_id):
        self.conversation_collection.update_one(
            {"_id": ObjectId(chat_id)},
            {"$set": {"previousUnstructuredDataLabelMappingConversationId": ObjectId(previous_conversation_id)}},
        )

    def get_recent_analysis(self, user_info):
        """
        Retrieves a list of recent conversation analyses, including both in-progress and completed/stopped conversations.

        The function executes a MongoDB aggregation pipeline to filter and sort conversations based on their status
        and type. The results are grouped into 'inProgress' and 'completedOrStopped' categories, and a maximum of
        five records are returned, prioritizing in-progress conversations.

        Returns:
            - GeneralResponse: A response object containing either the list of recent analyses in the 'data' field
            with detailed information such as conversation ID, title, status, type, updated date, and view status,
            or a failure message in case of an error.
        """
        user_id = user_info["id"]
        user_type = user_info["userType"]
        pipeline = [
            {
                "$match": {
                    "status": {
                        "$in": [
                            ConversationStatus.IN_PROGRESS.value,
                            ConversationStatus.COMPLETED.value,
                            ConversationStatus.STOPPED.value,
                        ]
                    },
                    "type": {
                        "$in": [
                            ConversationType.CHAT_BOT_CONVERSATION.value,
                            ConversationType.INSIGHT_GEN_CONVERSATION.value,
                        ]
                    },
                    "userId": ObjectId(user_id),
                }
            },
            {"$project": {"_id": 1, "status": 1, "name": 1, "type": 1, "updatedAt": 1, "isViewed": 1}},
            {
                # Split the data into two groups: one for IN_PROGRESS and another for COMPLETED/STOPPED
                "$facet": {
                    "inProgress": [
                        {"$match": {"status": ConversationStatus.IN_PROGRESS.value}},
                        {
                            "$sort": {
                                "updatedAt": -1
                            }  # Sort the IN_PROGRESS records by createdAt (or any other criteria)
                        },
                    ],
                    "completedOrStopped": [
                        {
                            "$match": {
                                "status": {
                                    "$in": [ConversationStatus.COMPLETED.value, ConversationStatus.STOPPED.value]
                                }
                            }
                        },
                        {"$sort": {"updatedAt": -1}},  # Sort the completed/stopped records by createdAt
                    ],
                }
            },
            {
                "$project": {
                    "inProgress": 1,
                    "completedOrStopped": {
                        "$cond": {
                            "if": {"$gte": [{"$size": "$inProgress"}, 5]},
                            "then": [],  # If there are >= 5 IN_PROGRESS, don't show any completed/stopped records
                            "else": {
                                "$slice": ["$completedOrStopped", {"$subtract": [5, {"$size": "$inProgress"}]}]
                            },  # Limit to 5 records if < 5 IN_PROGRESS
                        }
                    },
                }
            },
            {
                # Merge the results back into a single array
                "$project": {"result": {"$concatArrays": ["$inProgress", "$completedOrStopped"]}}
            },
            {
                # Flatten the results array
                "$unwind": "$result"
            },
            {"$replaceRoot": {"newRoot": "$result"}},
            {
                "$project": {
                    "_id": {"$toString": "$_id"},  # Convert ObjectId to string
                    "title": "$name",
                    "status": 1,
                    "type": 1,
                    "updatedAt": 1,
                    "isViewed": 1,
                }
            },
        ]
        try:
            if user_type == UserType.USER_TYPE_SUPER_ADMIN.value:
                match_stage = pipeline[0].get("$match", {})
                # Remove the userId filter for super admin
                if "userId" in match_stage:
                    del match_stage["userId"]
            response = list(self.conversation_collection.aggregate(pipeline))
            for rec in response:
                rec["progress"] = self.calculate_progress(rec["_id"])
            return GeneralResponse(success=True, data={"recent_analysis": response})
        except Exception as e:
            logger.error(
                f"ConversationService | get_recent_analysis | error occurred while fetching conversations: {e}"
            )
            return GeneralResponse(success=False, message=str(e))

    def reset_conversation_status_on_startup(self):
        """
        This function resets the status of conversations to "FAILED" for all conversations
        currently marked as "IN_PROGRESS". It is intended to be called on startup to
        ensure that the server is running from a clean state. The status update is
        accompanied by a timestamp indicating when the update occurred.
        """

        self.conversation_collection.update_many(
            {"status": ConversationStatus.IN_PROGRESS.value},
            {
                "$set": {
                    "status": ConversationStatus.FAILED.value,
                    "updatedAt": datetime.now(timezone.utc),
                }
            },
        )
        return

    def reset_session_status_on_startup(self):
        """
        This function resets the status of sessions to "FAILED" for all sessions
        currently marked as "IN_PROGRESS". It is intended to be called on startup to
        ensure that the server is running from a clean state. The status update is
        accompanied by a timestamp indicating when the update occurred.
        """
        self.sessions_collection.update_many(
            {"status": SessionStatus.IN_PROGRESS.value},
            {
                "$set": {
                    "status": SessionStatus.FAILED.value,
                    "updatedAt": datetime.now(timezone.utc),
                }
            },
        )
        return

    def set_conversation_viewed(self, conversation_id: str, isViewed: bool):
        """
         This function is used to update the 'isViewed' field of a conversation when a user views a conversation.

        Parameters:
            - conversation_id: str, conversation id.
            - isViewed: bool, the new value for the 'isViewed' field.

        Returns:
            - None
        """
        self.conversation_collection.update_one({"_id": ObjectId(conversation_id)}, {"$set": {"isViewed": isViewed}})
        logger.info(f"ConversationService | set_conversation_viewed | user viewed conversation:{conversation_id}")
        return

    def calculate_progress(self, conversation_id):
        return 0

    def stop_conversation(self, conversation_id):
        """
        This function stops a conversation and updates the status of the conversation record to STOPPED
        in the database. This is used when a user manually stops a conversation from the UI.

        Parameters:
            - conversation_id: str, conversation id.

        Returns:
            - None
        """
        self.conversation_collection.update_one(
            {"_id": ObjectId(conversation_id)}, {"$set": {"status": ConversationStatus.STOPPED.value}}
        )
        logger.info(
            f"ConversationService | stop_conversation | changed status of the conversation record to stopped in conversation:{conversation_id}"
        )
        return

    def get_session_input_code_answer(self, conversation_id, session_id, user_info):
        """
        Retrieves the input, code, and answer associated with a specific session within a conversation.

        Args:
            conversation_id (str): The ID of the conversation.
            session_id (str): The ID of the session within the conversation.

        Returns:
            dict: A dictionary containing session_user_input, session_code, session_answer, session_answer_generated_at, and user_inputs_before_session.
        """

        logger.debug(
            f"get_session_input_code_answer | N/A | conversation_id: {conversation_id}, session_id: {session_id}"
        )

        try:
            message_list = self.get_message_list(conversation_id, user_info["id"], userType=user_info.get("userType"))

            if not message_list:
                logger.error(
                    f"get_session_input_code_answer | N/A | Message list not found for conversation_id: {conversation_id}, session_id: {session_id}"
                )
                return None

            formatted_messages_list = self.format_message_list(message_list)

            if not formatted_messages_list:
                logger.error(
                    f"get_session_input_code_answer | N/A | Formatted message list not found for conversation_id: {conversation_id}, session_id: {session_id}"
                )
                return None

            session_data = [message for message in formatted_messages_list if message["sessionId"] == session_id]

            if not session_data:
                logger.error(
                    f"get_session_input_code_answer | N/A | Session data not found for conversation_id: {conversation_id}, session_id: {session_id}"
                )
                return None

            session_user_input = ""
            session_code = ""
            session_answer = []
            session_answer_generated_at = None
            user_inputs_before_session = []

            # only one user input can be exist for a session
            for data in session_data:
                if data["role"] == "user":
                    contents = data.get("contents", [])
                    if contents and isinstance(contents, list) and len(contents) > 0:
                        content_obj = contents[0]
                        if isinstance(content_obj, dict):
                            session_user_input = content_obj.get("content", "")
                    break

            # last occurrence of python content is the session_code among all "action" sectionType
            action_contents = []
            for data in session_data:
                if data["sectionType"] == "action":
                    contents = data.get("contents", [])
                    if isinstance(contents, list):
                        for content in contents:
                            if isinstance(content, dict):
                                action_contents.append(content)

            for content in reversed(action_contents):
                # TODO: Get the latest python code from conversation object
                if content["contentType"] == "text" and "```python" in content["content"]:
                    session_code, logic = get_python_actions(content["content"])
                    break

            # only one answer can be exist for a session
            for data in session_data:
                if data["sectionType"] == "answer":
                    session_answer = data.get("contents", [])
                    session_answer_generated_at = data.get("createdAt", None)
                    break

            if not session_user_input:
                logger.error(
                    f"get_session_input_code_answer | N/A | Input not found for conversation_id: {conversation_id}, session_id: {session_id}"
                )
                return None
            if not session_code:
                logger.error(
                    f"get_session_input_code_answer | N/A | Code not found for conversation_id: {conversation_id}, session_id: {session_id}"
                )
                return None
            if not session_answer:
                logger.error(
                    f"get_session_input_code_answer | N/A | Answer graphs not found for conversation_id: {conversation_id}, session_id: {session_id}"
                )
                return None

            # find user inputs before given session
            for data in formatted_messages_list:
                if data["sessionId"] >= session_id:
                    break
                if data["role"] == "user":
                    contents = data.get("contents", [])
                    if contents and isinstance(contents, list) and len(contents) > 0:
                        content_obj = contents[0]
                        if isinstance(content_obj, dict):
                            _user_input = content_obj.get("content", "")
                            if _user_input:
                                user_inputs_before_session.append(_user_input)

            return {
                "session_user_input": session_user_input,
                "session_code": session_code,
                "session_answer": session_answer,
                "session_answer_generated_at": session_answer_generated_at,
                "user_inputs_before_session": user_inputs_before_session,
            }

        except Exception as e:
            logger.error(
                f"get_session_input_code_answer | N/A | Unexpected error during aggregation: {e}, traceback: {traceback.format_exc()}"
            )
            return None

    def add_insight_data_to_history(self, conversation_instance: Conversation, insight_board_id: str):

        dashboard_insight = self.insight_dashboard_collection.get_one_document({"_id": ObjectId(insight_board_id)})

        if dashboard_insight is None:
            logger.error(
                f"ConversationService.add_insight_data_to_history | Dashboard insight not found for id: {insight_board_id}"
            )
            return GeneralResponse(success=False, message="Dashboard insight not found")

        user_input = dashboard_insight["sessionUserInput"]
        code = dashboard_insight["sessionCode"]
        answer = dashboard_insight["sessionAnswer"]

        conversation_instance.add_to_history(
            {"role": "user", "content": user_input, "contentType": "text"},
            True,
            False,
            False,
            True,
        )
        conversation_instance.add_to_history(
            {"role": "assistant", "content": code, "contentType": "text"},
            True,
            False,
            False,
            True,
        )
        conversation_instance.add_to_history(
            {"role": "assistant", "content": str(answer), "contentType": "text"},
            True,
            False,
            False,
            True,
        )

        insight_query = dashboard_insight.get("insightQuery", None)

        if not insight_query:
            logger.error(
                f"ConversationService.add_insight_data_to_history | Insight query not found for id: {insight_board_id}"
            )
            return GeneralResponse(success=False, message="Insight query not found")

        conversation_instance.add_to_history(
            {"role": "user", "content": insight_query, "contentType": "text"},
            True,
            False,
            False,
            True,
        )

        insight_id = dashboard_insight.get("insightId", None)

        if not insight_id:
            logger.error(
                f"ConversationService.add_insight_data_to_history | Insight id not found for id: {insight_board_id}"
            )
            return GeneralResponse(success=False, message="Insight id not found")

        insight_report = self.insight_report_collection.get_one_document({"insight_id": ObjectId(insight_id)})

        if insight_report is None:
            logger.error(
                f"ConversationService.add_insight_data_to_history | Insight report not found for id: {insight_board_id}"
            )
            return GeneralResponse(success=False, message="Insight report not found")

        key_findings = insight_report.get("report", {}).get("keyFindings", {})

        if not key_findings:
            logger.error(
                f"ConversationService.add_insight_data_to_history | Key findings not found for id: {insight_board_id}"
            )
            return GeneralResponse(success=False, message="Key findings not found")

        formatted_key_findings = key_findings.get("shortDescription", "") + "\n\n"

        main_facts = key_findings.get("mainFacts", [])

        for fact in main_facts:
            formatted_key_findings += f"{ fact.get('id', '')}. { fact.get('fact', '')}\n"

        conversation_instance.add_to_history(
            {"role": "assistant", "content": formatted_key_findings, "contentType": "text"},
            True,
            False,
            False,
            True,
        )

    def attach_conversation_as_insight_followup(self, insight_board_id, conversation_id, content):
        """
        Description: Attach a conversation as an insight follow-up.

        Parameters:
            - insight_board_id: str, the unique identifier for the insight board.
            - conversation_id: str, the unique identifier for the conversation.
            - content: str, the content to be attached as an insight follow-up.

        Returns:
            - None
        """

        self.insight_dashboard_collection.find_one_push(
            {"_id": ObjectId(insight_board_id)},
            {"followUps": {"conversationId": ObjectId(conversation_id), "query": content}},
        )

    def deprecated_get_conversation_history_blocks(self, conversation_id, user_id, user_type, session_id=None):

        conversation_db_obj = self.get_conversation_data(conversation_id)

        sessions = []
        default_session_type = SessionType.ANALYSIS.value  # last session type of the conversation

        res = {
            "sessions": sessions,
            "default_session_type": default_session_type,
            "conversation_id": conversation_id,
            "title": conversation_db_obj.get("name", ""),
            "is_favourite": conversation_db_obj.get("isFavourite", False),
        }

        try:

            filter_criteria = {"conversation_id": ObjectId(conversation_id)}

            if session_id:
                filter_criteria["session_id"] = session_id

            data_blocks_list = self.data_block_collection.get_documents(
                filter_criteria,
                sort_criteria_list=[("_id", pymongo.ASCENDING)],
            )

            if not data_blocks_list:
                return res

            sessions = []

            session_id = data_blocks_list[0].get(
                "session_id", None
            )  # initially take the session id from the first data block

            if not session_id:
                return res

            answer = []  # list of answer blocks (MARKDOWN and MEDIA blocks)
            tasks = ""  # tasks block (MARKDOWN blocks)
            sources = []  # list of sources blocks
            query = ""  # user query block
            tabs = []  # list of tabs blocks
            blocks = []  # list of all blocks

            for data_block in data_blocks_list:

                _session_id = data_block.get("session_id", None)
                _session_type = data_block.get("session_type", default_session_type)
                if _session_type != default_session_type:
                    default_session_type = _session_type

                # if the session id is changed, then add the previous session to the sessions list and reset the answer, tasks, sources, query, tabs
                if _session_id != session_id:
                    sessions.append(
                        {
                            "conversation_id": conversation_id,
                            "session_id": session_id,
                            "session_type": data_block.get("session_type", None),
                            "answer": answer,
                            "tasks": tasks,
                            "sources": sources,
                            "query": query,
                            "tabs": tabs,
                            "blocks": blocks,
                        }
                    )
                    answer = []
                    tasks = ""
                    sources = []
                    query = ""
                    tabs = []
                    blocks = []
                    session_id = _session_id

                _blocks = data_block.get("blocks", [])

                if not _blocks:
                    continue

                for block in _blocks:

                    # TODO: uncomment below line when the frontend is ready. After that we can remove the below repeated logic (exists in both frontend and backend). Also, we can implement a logic to merge blocks to avoid duplicated data such as same block_ids
                    # blocks.append(block)

                    _block_id = block.get("block_id", None)
                    _block_type = block.get("block_type", None)
                    _block_tab_types = block.get("tab_types", [])
                    _block_operation = block.get("operation", None)
                    _block_status = block.get("status", None)
                    _block_data = block.get("data", None)

                    # add each block to respective variable based on the frontend tab types

                    # user question/query tab
                    if FrontendTabContentType.QUERY.tab_type_value in _block_tab_types:
                        # user question/query tab only contain USER_QUESTION blocks
                        if _block_type == FrontendBlockType.USER_QUESTION.type_value:
                            if _block_operation == StreamChunkOperation.ADD.value:
                                query += _block_data
                            elif _block_operation == StreamChunkOperation.REPLACE.value:
                                query = _block_data
                        continue  # QUERY tab type only contain data belongs to QUERY tab

                    # tabs tab
                    if FrontendTabContentType.TABS.tab_type_value in _block_tab_types:
                        # tabs tab only contain TAB_CONTENT blocks
                        if _block_type == FrontendBlockType.TAB_CONTENT.type_value:
                            if _block_operation == StreamChunkOperation.ADD.value:
                                for _tab in _block_data:
                                    tabs.append(_tab)
                            elif _block_operation == StreamChunkOperation.REPLACE.value:
                                tabs = _block_data
                        continue  # TABS tab type only contain data belongs to TABS tab

                    # data sources tab
                    if FrontendTabContentType.SOURCES.tab_type_value in _block_tab_types:
                        # data sources tab only contain DATA_SOURCES blocks
                        if _block_type == FrontendBlockType.DATA_SOURCES.type_value:
                            if _block_operation == StreamChunkOperation.ADD.value:
                                for _source in _block_data:
                                    sources.append(_source)
                            elif _block_operation == StreamChunkOperation.REPLACE.value:
                                sources = _block_data

                    # tasks tab
                    if FrontendTabContentType.TASKS.tab_type_value in _block_tab_types:
                        # tasks tab only contain MARKDOWN blocks
                        if _block_type == FrontendBlockType.MARKDOWN.type_value:
                            if _block_operation == StreamChunkOperation.ADD.value:
                                tasks += _block_data
                            elif _block_operation == StreamChunkOperation.REPLACE.value:
                                tasks = _block_data

                    # answer tab
                    if FrontendTabContentType.FINAL_CONCLUSION.tab_type_value in _block_tab_types:
                        # answer tab contain both MARKDOWN, DATA_SOURCE and MEDIA blocks
                        # but all are add operation blocks with new block ids
                        block["tab_types"] = [
                            FrontendTabContentType.ANSWER.tab_type_value
                        ]  # changed backend only known FINAL_CONCLUSION as frontend known ANSWER
                        answer.append(block)

            sessions.append(
                {
                    "conversation_id": conversation_id,
                    "session_id": session_id,
                    "session_type": data_block.get("session_type", None),
                    "status": SessionStatus.COMPLETED.value,
                    "answer": answer,
                    "tasks": tasks,
                    "sources": sources,
                    "query": query,
                    "tabs": tabs,
                }
            )

            # order tabs and make status of tabs in tabs list as completed

            order_precedence = [
                FrontendTabContentType.ANSWER.tab_type_value,
                FrontendTabContentType.SOURCES.tab_type_value,
                FrontendTabContentType.TASKS.tab_type_value,
            ]

            for session in sessions:
                session["tabs"] = sorted(
                    session["tabs"],
                    key=lambda x: (
                        order_precedence.index(x["id"]) if x["id"] in order_precedence else len(order_precedence)
                    ),
                )

            for session in sessions:
                for tab in session["tabs"]:
                    tab["status"] = FrontendBlockStatus.COMPLETED.value

            res["sessions"] = sessions

            # conversation mark as viewed
            self.set_conversation_viewed(conversation_id, True)

            return res

        except Exception as e:
            logger.error(
                f"ConversationService.get_conversation_history_blocks | Unexpected error during aggregation: {e}, traceback: {traceback.format_exc()}"
            )
            return res

    def initialize_session_in_db(self, conversation_id, session_type, user_id, user_input) -> GeneralResponse[str]:
        res = self.sessions_collection.insert_one(
            {
                "conversation_id": ObjectId(conversation_id),
                "type": session_type,
                "user_id": ObjectId(user_id),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "status": SessionStatus.IN_PROGRESS.value,
                "user_input": user_input,
            }
        )

        if res.inserted_id:
            return GeneralResponse(
                success=True,
                message="Session initialized successfully",
                data=str(res.inserted_id),
            )
        else:
            return GeneralResponse(
                success=False,
                message="Failed to initialize new session",
            )

    def save_session_user_reaction(self, conversation_id, reaction_info):
        session_id = reaction_info.sessionId
        reaction = reaction_info.reaction

        if not session_id:
            logger.error(
                f"ConversationService.save_session_user_reaction | Session id not found for conversation {conversation_id}"
            )
            return GeneralResponse(success=False, message="Session id not found")

        if not reaction:
            logger.error(
                f"ConversationService.save_session_user_reaction | Reaction not found for conversation {conversation_id}, session id: {session_id}"
            )
            return GeneralResponse(success=False, message="Reaction not found")

        # update the session with the reaction
        self.sessions_collection.update_one(
            {"_id": ObjectId(session_id)},
            {"$set": {"user_reaction": reaction}},
        )

        return GeneralResponse(success=True, message="User reaction saved successfully")

    def save_add_to_dashboard_session(self, conversation_id, add_to_dashboard_info):
        session_id = add_to_dashboard_info.sessionId
        dashboard_insight_info = add_to_dashboard_info.frequencyInfo

        if not session_id:
            logger.error(
                f"ConversationService.save_add_to_dashboard_session | Session id not found for conversation {conversation_id}"
            )
            return GeneralResponse(success=False, message="Session id not found")

        if not add_to_dashboard_info:
            logger.error(
                f"ConversationService.save_add_to_dashboard_session | Frequency info not found for conversation {conversation_id}"
            )
            return GeneralResponse(success=False, message="Frequency info not found")

        # update the session with the frequency info
        self.sessions_collection.update_one(
            {"_id": ObjectId(session_id)},
            {"$set": {"dashboard_insight_info": dashboard_insight_info}},
        )

    def get_conversation_history_blocks(
        self, conversation_id, user_id, user_type, session_id=None
    ) -> GeneralResponse[dict]:
        conversation_db_obj = self.get_conversation_data(conversation_id)

        data_block_visibility = get_data_block_visibility_list(user_type)

        if not conversation_db_obj:
            return GeneralResponse(success=False, message=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)

        # if user is super admin
        # or requested user id is the owner of the conversation
        # or requested user is a shared user of the conversation,
        # then allow to get the conversation history blocks
        is_super_admin = user_type == UserType.USER_TYPE_SUPER_ADMIN.value
        is_owner = str(conversation_db_obj.get("userId", "")) == str(user_id)
        is_shared_user = str(user_id) in [str(user_id) for user_id in conversation_db_obj.get("shared_users", [])]

        if not (is_super_admin or is_owner or is_shared_user):
            logger.error(
                f"ConversationService.get_conversation_history_blocks | User {user_id} is not authorized to get conversation history blocks for conversation {conversation_id}"
            )
            return GeneralResponse(success=False, message=ChatAppUserMessages.NOT_CHAT_OWNER.value)

        # conversation mark as viewed
        self.set_conversation_viewed(conversation_id, True)

        filter_criteria = {"conversation_id": ObjectId(conversation_id)}

        if session_id:
            filter_criteria["_id"] = ObjectId(session_id)

        # fetch sessions from db
        _sessions = self.sessions_collection.get_documents(
            filter_criteria,
            sort_criteria_list=[("_id", pymongo.ASCENDING)],
        )

        if not _sessions:
            return GeneralResponse(success=False, message=ChatAppUserMessages.CHAT_CAN_NOT_FIND_SESSIONS.value)

        all_sessions = []
        default_session_type = SessionType.ANALYSIS.value  # last session type of the conversation

        res = {
            "sessions": all_sessions,
            "default_session_type": default_session_type,
            "conversation_id": conversation_id,
            "status": conversation_db_obj.get("status"),
            "title": conversation_db_obj.get("name", ""),
            "is_favourite": conversation_db_obj.get("isFavourite", False),
        }

        show_pin_index = None  # index of the session to show the add to dashboard pin button

        try:

            # fetch data blocks from db
            for session_index, _session in enumerate(_sessions):

                res["default_session_type"] = _session.get("type", default_session_type)

                if (
                    _session.get("type") == SessionType.ANALYSIS.value
                    or _session.get("type") == SessionType.COMPLEX_ANALYSIS.value
                ) and _session.get("session_answer_list"):
                    show_pin_index = session_index

                session_data_blocks = []

                # fetch data blocks from db
                _data_blocks_lists = self.data_block_collection.get_documents(
                    {"session_id": _session["_id"], "user_visibility": {"$in": data_block_visibility}},
                    sort_criteria_list=[("_id", pymongo.ASCENDING)],
                )

                if not _data_blocks_lists:
                    continue

                # Collect all blocks from all data block lists
                all_blocks = []
                for _data_blocks_list in _data_blocks_lists:
                    _data_blocks = _data_blocks_list.get("blocks", [])
                    if _data_blocks:
                        all_blocks.extend(_data_blocks)

                # Merge blocks using the utility method
                if all_blocks:
                    session_data_blocks = Session.merge_data_blocks(
                        all_blocks,
                        str(_session["_id"]),
                        _session.get("status", SessionStatus.COMPLETED.value),
                        _session.get("user_reaction", UserReaction.PENDING.value),
                    )

                formatted_session = self.refactor_session_data_blocks(
                    session_data_blocks
                )  # Temporary formatting as frontend requires. need to remove after frontend is ready

                duration = 0  # in seconds

                if _session.get("status") == SessionStatus.IN_PROGRESS.value:
                    duration = (
                        datetime.now(timezone.utc) - _session.get("created_at").replace(tzinfo=timezone.utc)
                    ).total_seconds()

                all_sessions.append(
                    {
                        "conversation_id": conversation_id,
                        "session_id": str(_session["_id"]),
                        "session_type": _session.get("type"),
                        "status": _session.get("status"),
                        "blocks": session_data_blocks,
                        "duration": duration,
                        **formatted_session,
                    }
                )

            if show_pin_index is not None:
                res["sessions"][show_pin_index]["show_pin"] = True

            return GeneralResponse(success=True, message="Session data blocks retrieved successfully", data=res)
        except Exception as e:
            logger.error(
                f"ConversationService.get_conversation_history_blocks | Unexpected error during aggregation: {e}, traceback: {traceback.format_exc()}"
            )
            return GeneralResponse(
                success=False, message=ChatAppUserMessages.FAILED_TO_RETRIEVE_CONVERSATION_HISTORY_BLOCKS.value
            )

    def refactor_session_data_blocks(self, session_data_blocks):

        answer = []  # list of answer blocks (MARKDOWN and MEDIA blocks)
        tasks = ""  # tasks block (MARKDOWN blocks)
        sources = []  # list of sources blocks
        query = ""  # user query block
        tabs = []  # list of tabs blocks

        res = {
            "answer": answer,
            "tasks": tasks,
            "sources": sources,
            "query": query,
            "tabs": tabs,
        }

        for block in session_data_blocks:
            _block_id = block.get("block_id", None)
            _block_type = block.get("block_type", None)
            _block_tab_types = block.get("tab_types", [])
            _block_operation = block.get("operation", None)
            _block_status = block.get("status", None)
            _block_data = block.get("data", None)

            # add each block to respective variable based on the frontend tab types

            # user question/query tab
            if FrontendTabContentType.QUERY.tab_type_value in _block_tab_types:
                # user question/query tab only contain USER_QUESTION blocks
                if _block_type == FrontendBlockType.USER_QUESTION.type_value:
                    if _block_operation == StreamChunkOperation.ADD.value:
                        query += _block_data
                    elif _block_operation == StreamChunkOperation.REPLACE.value:
                        query = _block_data
                continue  # QUERY tab type only contain data belongs to QUERY tab

            # tabs tab
            if FrontendTabContentType.TABS.tab_type_value in _block_tab_types:
                # tabs tab only contain TAB_CONTENT blocks
                if _block_type == FrontendBlockType.TAB_CONTENT.type_value:
                    if _block_operation == StreamChunkOperation.ADD.value:
                        for _tab in _block_data:
                            tabs.append(_tab)
                    elif _block_operation == StreamChunkOperation.REPLACE.value:
                        tabs = _block_data
                continue  # TABS tab type only contain data belongs to TABS tab

            # data sources tab
            if FrontendTabContentType.SOURCES.tab_type_value in _block_tab_types:
                # data sources tab only contain DATA_SOURCES blocks
                if _block_type == FrontendBlockType.DATA_SOURCES.type_value:
                    if _block_operation == StreamChunkOperation.ADD.value:
                        for _source in _block_data:
                            sources.append(_source)
                    elif _block_operation == StreamChunkOperation.REPLACE.value:
                        sources = _block_data

            # tasks tab
            if FrontendTabContentType.TASKS.tab_type_value in _block_tab_types:
                # tasks tab only contain MARKDOWN blocks
                if _block_type == FrontendBlockType.MARKDOWN.type_value:
                    if _block_operation == StreamChunkOperation.ADD.value:
                        tasks += _block_data
                    elif _block_operation == StreamChunkOperation.REPLACE.value:
                        tasks = _block_data

            # answer tab
            if FrontendTabContentType.FINAL_CONCLUSION.tab_type_value in _block_tab_types:
                # answer tab contain both MARKDOWN, DATA_SOURCE and MEDIA blocks
                # but all are add operation blocks with new block ids
                # block["tab_types"] = [
                #     FrontendTabContentType.ANSWER.tab_type_value
                # ]  # changed backend only known FINAL_CONCLUSION as frontend known ANSWER
                answer.append(block)

        res["answer"] = answer
        res["tasks"] = tasks
        res["sources"] = sources
        res["query"] = query
        res["tabs"] = tabs

        return res

    def update_conversation_status(self, conversation_id: str, status: str):
        self.conversation_collection.update_one(
            {"_id": ObjectId(conversation_id)},
            {"$set": {"status": status}},
        )
