"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class IterativeProblemSolverAgent
* @description Agent responsible for solving complex problems iteratively using available tools
* <AUTHOR>
"""

import asyncio
import base64
import json
import re
import traceback
from logging import Logger
import os
from typing import List
from bson import ObjectId
from pydantic import BaseModel
from agents import Agent, ModelBehaviorError, RunResult, Runner
from openai.types.responses import ResponseTextDeltaEvent
from services.base_agent_service import BaseAgent

from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationConclusion,
    ConversationLLMStatus,
    ConversationStatus,
    DataBlockHandlerType,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    LLMAgentType,
    MessageContentType,
    SectionType,
    AgentToolName,
)
from PIL import Image
from services.conversation_service import ConversationService
from models.conversation import Conversation
from services.tools.base_tool import BaseTool, ToolOutput

from services.shared.base_state_handler_agent import BaseState<PERSON>andlerAgent
from utils.excel_file_handler import process_xlsx_file, process_xls_file, process_csv_file
from utils.llm_utils import write_llm_cache, read_llm_cache
from utils.misc_utils import csv_to_markdown
from services.tools.response_archive.response_data_archive import (
    IMAGE_DATA_PREFIX,
    IMAGE_FILE_PREFIX,
    ResponseDataArchive,
)


class AgentInstruction(BaseModel):
    tool_name: str  # One of: 'SQL_Data_Retrieval_Tool' | 'Data_Processing_Tool' | 'Metadata_Finder'
    data_source_name: str  # source_name from 'metadata'
    data_file_names: list[str]
    instruction: str  # Self-contained, natural language instruction for the agent
    source_excel_file_name: str  # Original Excel workbook name
    target_excel_file_name: str  # Destination Excel workbook name to be created as a copy
    sheet_name: str  # Excel sheet name
    columns_to_update: list[str]  # List of column headers to update
    key_columns: list[str]  # List of key columns to match records
    archive_reference_id: str  # Unique ID for the tool instruction


class ArchivedContentReference(BaseModel):
    tool_name: str
    reference_id: str
    content_description: str


class ProblemSolverOutput(BaseModel):
    current_reasoning: str
    #    archived_content_references: List[ArchivedContentReference]
    is_final_cycle: bool
    updated_plan: str
    next_steps_guidance: str
    tool_instructions: List[AgentInstruction]
    final_answer: str
    analysis_title: str
    knowledge_blocks_used: list[str]
    output_files: list[str]


FRONTEND_URL = os.getenv("FRONTEND_URL")


class IterativeProblemSolverAgent(Agent, BaseStateHandlerAgent):
    def __init__(
        self, data_source_name: str = "", default_model: str = "o3", system_prompt: str = "", history_depth=1
    ):

        # Read model name from env variable
        model_name = os.getenv("MODEL_COMPLEX_ANALYSIS", default_model)
        # Set the default system prompt if not provided
        if not system_prompt:
            with open(
                "instructions/orchestration_agent/iterative_problem_solver/system_instructions.txt",
                "r",
                encoding="utf-8",
            ) as file:
                system_prompt = file.read()

        Agent.__init__(
            self,
            name="Complex Problem Solver",
            instructions=system_prompt,
            model=model_name,
            output_type=ProblemSolverOutput,
        )
        BaseStateHandlerAgent.__init__(
            self, data_source_name, model_name, AgentState.COMPLEX_REASONING, history_depth=history_depth
        )
        self.conversation_handler = ConversationService()
        self.model_name = model_name
        self.content_archive_tool_instance = ResponseDataArchive()
        self.is_llm_cached = os.environ.get("IS_LLM_CACHE_ENABLED", "false").lower() == "true"
        self.is_skip_attachment_scan = False  # Default
        self.init_tools()

    def init_tools(self):
        """
        Initialize the tools dictionary with instances of all available tools.
        """
        from services.tools.knowledge_finder.knowledge_finder_agent import KnowledgeFinder
        from services.tools.data_finder.data_finder_agent import DataFinderAgent
        from services.tools.excel.excel_updater_agent import ExcelUpdaterAgent
        from services.tools.data_processor.data_processor_agent import DataProcessorAgent
        from services.tools.data_retriever.sql_data_retriever_agent import SQLDataRetrieverAgent

        from services.tools.data_retriever.document_data_retriever_agent import DocumentDataQueryAgent

        self.tools_dict: dict[str, dict] = {
            AgentToolName.KNOWLEDGE_FINDER.value: {
                "instance": KnowledgeFinder(),
                "display_text": FrontendBlockDataMessages.INSTRUCTIONS_TO_KNOWLEDGE_FINDER_TOOL.value,
            },
            AgentToolName.DATA_PROCESSING_TOOL.value: {
                "instance": DataProcessorAgent(),
                "display_text": FrontendBlockDataMessages.INSTRUCTIONS_TO_DATA_PROCESSING_TOOL.value,
            },
            AgentToolName.SQL_DATA_RETRIEVAL_TOOL.value: {
                "instance": SQLDataRetrieverAgent(),
                "display_text": FrontendBlockDataMessages.INSTRUCTIONS_TO_SQL_DATA_RETRIEVAL_TOOL.value,
            },
            AgentToolName.METADATA_FINDER.value: {
                "instance": DataFinderAgent(),
                "display_text": FrontendBlockDataMessages.INSTRUCTIONS_TO_METADATA_FINDER_TOOL.value,
            },
            AgentToolName.EXCEL_UPDATER.value: {
                "instance": ExcelUpdaterAgent(),
                "display_text": FrontendBlockDataMessages.INSTRUCTIONS_TO_EXCEL_UPDATER_TOOL.value,
            },
            AgentToolName.MEMORY_RECALL.value: {
                "instance": self.content_archive_tool_instance,
                "display_text": FrontendBlockDataMessages.INSTRUCTIONS_TO_MEMORY_RECALL_TOOL.value,
            },
            AgentToolName.USER_INPUT.value: {
                "instance": None,
                "display_text": FrontendBlockDataMessages.INSTRUCTIONS_TO_USER_INPUT_TOOL.value,
            },
            AgentToolName.DOCUMENT_DATA_RETRIEVER.value: {
                "instance": DocumentDataQueryAgent(),
                "display_text": FrontendBlockDataMessages.INSTRUCTIONS_TO_DOCUMENT_DATA_RETRIEVER_TOOL.value,
            },
        }

    async def run_iteration(
        self,
        conversation: Conversation,
        cycle_index: int,
        input_data: list,
        is_metadata_retrieved: bool,
        chat_log,
    ):
        def input_data_extender(_input_data: list):
            input_data.extend(_input_data)
            self.update_followup_history(conversation, chat_log, {"agent_inputs": input_data})
            return input_data

        # Execute the next iteration using Agent SDK
        result: RunResult = None
        is_user_input = False
        chat_log.debug(f"Running the cycle {cycle_index} + input length: {len(input_data)}")
        try:
            conversation.active_session_send_analysis_step(AnalysisStep.REASONING_AND_PLANNING)
            conversation.active_session.add_section(SectionType.QUERY_PLAN.value)
            # Update the analysis box
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.REASONING_AND_PLANNING.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            )
            agent_input = self._prepare_agent_input_with_summarized_content(input_data, cycle_index)
            chat_log.debug(f"Cycle {cycle_index} - Input:\n {json.dumps(agent_input, indent=2)}\n\n")
            # result = Runner.run_sync(self, input=agent_input, max_turns=15)
            # TODO: Use caching if enabled
            # result = None
            # if self.is_llm_cached:
            #     cache_read = read_llm_cache(input_data, conversation.chat_id, RunResult)
            #     if cache_read:
            #         result = cache_read
            #     else:
            #         result = Runner.run_sync(self, input=input_data, max_turns=15)
            #         write_llm_cache(input_data, result, conversation.chat_id)
            # else:
            #     result = Runner.run_sync(self, input=input_data, max_turns=15)

            # TODO: Streaming support
            result = Runner.run_streamed(self, input=agent_input, max_turns=15)
            all_chunks = ""
            async for event in result.stream_events():
                if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                    chunk_message = event.data.delta
                    all_chunks += chunk_message
                    conversation.persist_and_stream_handler(
                        block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                        block_type=FrontendBlockType.MARKDOWN,
                        block_data=chunk_message,
                        handler_type=DataBlockHandlerType.STREAM,
                        block_tab_types=[FrontendTabContentType.ANSWER],
                        user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
                    )
            # chat_log.debug(f"Master agent output:\n\n {all_chunks}\n\n")

            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=all_chunks + "\n\n",
                block_tab_types=[FrontendTabContentType.TASKS],
                handler_type=DataBlockHandlerType.PERSIST,
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )

            output: ProblemSolverOutput = result.final_output
            result.input = []  # To clear current input
            new_data = result.to_input_list()
            # input_data.extend(new_data)
            input_data_extender(new_data)

            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=str(output.current_reasoning) + "\n\n",
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.ALL_EXCLUDE_SUPER_ADMIN,
            )

            # Check if this is just a thought step
            if not output.is_final_cycle and not output.tool_instructions:
                # This means user input tool call
                output.tool_instructions = [
                    AgentInstruction(
                        tool_name=AgentToolName.USER_INPUT.value,
                        data_source_name="",
                        data_file_names=[],
                        instruction=output.next_steps_guidance
                        or output.final_answer
                        or "Please provide more information to continue.",
                        source_excel_file_name="",
                        target_excel_file_name="",
                        sheet_name="",
                        columns_to_update=[],
                        key_columns=[],
                        archive_reference_id="",
                    )
                ]
                # return {
                #     "result": output,
                #     "next_input": input_data,
                #     "is_complete": output.analysis_completed,
                #     "is_metadata_retrieved": is_metadata_retrieved,
                # }

            # Execute tool and get response for each tool instruction
            for agent_instruction in output.tool_instructions:

                if conversation.active_session.user_stopped_stream:
                    chat_log.debug("User stopped the session, aborting the tool execution")
                    break

                # Validate the tool name, if not return the problem as next input
                if agent_instruction.tool_name not in self.tools_dict:
                    input_data_extender(
                        [
                            {
                                "role": "user",
                                "content": f"Invalid tool name: {agent_instruction.tool_name}. Please correct it in the instruction {agent_instruction.instruction}.",
                            }
                        ]
                    )
                    continue

                tool_display_text = self.tools_dict.get(agent_instruction.tool_name)["display_text"]
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=tool_display_text,
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                )
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data="\n\n" + str(agent_instruction.instruction) + "\n\n",
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                    user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
                )

                # In case of user input tool, then break the loop and return the instruction as output
                if agent_instruction.tool_name == AgentToolName.USER_INPUT.value:
                    is_user_input = True
                    output.final_answer = agent_instruction.instruction
                    break
                # Validate data file names in case of Data processing tool
                if agent_instruction.tool_name == AgentToolName.DATA_PROCESSING_TOOL.value:
                    invalid_data_file_names = []
                    for data_file_name in agent_instruction.data_file_names:
                        if not os.path.exists(f"storage/public/{conversation.chat_id}/{data_file_name}.pkl"):
                            invalid_data_file_names.append(data_file_name)
                    if invalid_data_file_names:
                        input_data_extender(
                            [
                                {
                                    "role": "user",
                                    "content": f"Invalid data file names: {invalid_data_file_names}. Please correct it.",
                                }
                            ]
                        )
                        break
                tool_instance: BaseTool = self.tools_dict.get(agent_instruction.tool_name)["instance"]
                chat_log.debug(
                    f"Executing tool {agent_instruction.tool_name} with instruction: {agent_instruction.instruction}"
                )
                # Create clone of conversation
                conversation_clone, session_id = self.conversation_handler.create_conversation_and_session(
                    conversation.user_id,
                    agent_instruction.instruction,
                    LLMAgentType.AGENT_TYPE_ACTION,
                    conversation.active_session,
                    False,
                    conversation,
                    False,
                    True,
                )
                # Create log directory for the tool execution
                if not os.path.exists(f"./logs/{conversation.chat_id}"):
                    os.makedirs(f"./logs/{conversation.chat_id}")
                tool_response: ToolOutput = tool_instance.execute_task(
                    conversation_clone,
                    agent_instruction.data_source_name,
                    agent_instruction.data_file_names,
                    agent_instruction.instruction,
                    f"{conversation.chat_id}/",
                    {
                        "is_initial_metadata_pending": not is_metadata_retrieved,
                        "parent_agent_conversation_id": conversation.chat_id,
                        "source_excel_file_name": agent_instruction.source_excel_file_name,
                        "target_excel_file_name": agent_instruction.target_excel_file_name,
                        "sheet_name": agent_instruction.sheet_name,
                        "columns_to_update": agent_instruction.columns_to_update,
                        "key_columns": agent_instruction.key_columns,
                        "reference_id": agent_instruction.archive_reference_id,
                    },
                )
                chat_log.debug(f"Result from tool {agent_instruction.tool_name}:\n {tool_response}\n\n")
                if agent_instruction.tool_name == AgentToolName.METADATA_FINDER.value:
                    is_metadata_retrieved = True

                self.conversation_handler.copy_conversation_to_parent(conversation_clone, conversation)
                # Archive the tool result text for later reference
                results_with_summary = self.content_archive_tool_instance.summarize_results(
                    conversation,
                    AgentToolName(agent_instruction.tool_name),
                    cycle_index,
                    agent_instruction.instruction,
                    tool_response.result_text_list,
                    "user",
                    chat_log,
                )
                # Add the tool response to the thought chain
                input_data_extender(
                    [
                        {
                            "role": "user",
                            "content": f"Request to tool {agent_instruction.tool_name}: {agent_instruction.instruction}\n\n",
                        }
                    ]
                )
                input_data_extender(results_with_summary)

                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data="\n\n---\n\n**Result from "
                    + agent_instruction.tool_name
                    + "**:\n\n "
                    + str(tool_response.display_output)
                    + "\n\n---\n\n",
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                    user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
                )

        except Exception as e:
            chat_log.error(
                f"Exception in IterativeProblemSolverAgent | run_iteration: {e}\n: {traceback.format_exc()}"
            )
            # Re-invoke agent with error if this is ModelBehaviorError
            if isinstance(e, ModelBehaviorError):
                input_data_extender([{"role": "user", "content": f"Please fix this error and try again: {e}"}])
                return {
                    "result": "Error",
                    "next_input": input_data,
                    "is_complete": False,
                    "is_user_input_required": is_user_input,
                    "is_metadata_retrieved": is_metadata_retrieved,
                }
            # Check if this is context window issue, then abort
            if "context window" in str(e).lower():
                conversation.data_retrieval_status = ConversationConclusion.SYSTEM_FAILURE
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data="\n\nI'm sorry, I'm having trouble finding an answer for your question. The error is: "
                    + str(e)
                    + "\n\n",
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                )
                return {
                    "result": str(e),
                    "next_input": input_data,
                    "is_complete": True,
                    "is_user_input_required": is_user_input,
                    "is_metadata_retrieved": is_metadata_retrieved,
                }
            else:
                # Possible Temporary error - wait for 5 seconds and try again
                await asyncio.sleep(5)
                return {
                    "result": "Error",
                    "next_input": input_data,
                    "is_complete": False,
                    "is_user_input_required": is_user_input,
                    "is_metadata_retrieved": is_metadata_retrieved,
                }

        # Return the result
        return {
            "result": output,
            "next_input": input_data,
            "is_complete": output.is_final_cycle,
            "is_user_input_required": is_user_input,
            "is_metadata_retrieved": is_metadata_retrieved,
        }

    def _prepare_agent_input_with_summarized_content(self, input_data_list: list, current_cycle_index):
        agent_input = []
        input_data_list.append(
            {
                "cycle_index": current_cycle_index,
                "role": "user",
                "content": f"Cycle index: {current_cycle_index}",
                "reference_id": "",
            }
        )
        # Find if at least one of input data for previous cycle is from archive, if so we need to feed full context of both immediate and previous cycle
        # Otherwise we can feed full context of only the immediate previous cycle context
        full_result_cycle_count = 1
        for input_record in input_data_list:
            if (
                "cycle_index" in input_record
                and input_record["cycle_index"] == current_cycle_index - 1
                and "is_from_archive" in input_record
                and input_record["is_from_archive"]
            ):
                full_result_cycle_count = 2
                break

        for input_record in input_data_list:
            # The reasoning feedback handle separately by "id"
            if "id" in input_record:
                # append as it is
                agent_input.append(input_record)
                continue
            # We don't feed content retrieve from archive again if this is before full_result_cycle_count
            if (
                "is_from_archive" in input_record
                and input_record["is_from_archive"]
                and input_record["cycle_index"] < current_cycle_index - full_result_cycle_count
            ):
                continue
            # If the content is not summarized(no reference id), then feed the full content as it is
            # If the record belongs to the previous cycles up to full_result_cycle_count, then feed the full content too
            if (
                "reference_id" not in input_record
                or not input_record["reference_id"]
                or input_record["cycle_index"] >= current_cycle_index - full_result_cycle_count
            ):
                # Special case for images:
                if "content_type" in input_record and input_record["content_type"] == "image":
                    agent_input.append(
                        {
                            "role": input_record["role"],
                            "content": [
                                {
                                    "type": "input_image",
                                    "image_url": f"data:image/png;base64,{input_record['content']}",
                                }
                            ],
                        }
                    )
                else:
                    agent_input.append(
                        {
                            "role": input_record["role"] if "role" in input_record else "user",
                            "content": input_record["content"],
                        }
                    )
            else:
                # Feed the summary with reference
                content_text = f"Summary of result from {input_record['tool_name']}: {input_record['summary']}.\n\
                    To access the full result, use the Content Archive Tool with reference id: **{input_record['reference_id']}**."
                agent_input.append(
                    {
                        "role": input_record["role"],
                        "content": content_text,
                    }
                )

        return agent_input

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        cycle_count = 0
        is_metadata_retrieved = False
        agent_inputs_list = []
        thought_chain = []
        if conversation.is_continue_previous:
            # Loading all the previous state to start from that point
            for history_record in follow_up_history:
                agent_input = json.loads(history_record["content"])
                cycle_count = agent_input["cycle_index"] if "cycle_index" in agent_input else cycle_count
                agent_inputs_list.append(
                    {
                        "role": history_record["role"],
                        "content": agent_input["content"],
                        "cycle_index": cycle_count,
                        "reference_id": agent_input["reference_id"] if "reference_id" in agent_input else "",
                        "content_type": agent_input.get("content_type", "text"),
                        "tool_name": agent_input.get("tool_name", ""),
                        "is_from_archive": agent_input.get("is_from_archive", False),
                        "summary": agent_input.get("summary", ""),
                    }
                )
                # Set the metadata retrieved flag in case of followup to prevent ER diagram duplicating in followups
                if agent_input.get("tool_name", "") == AgentToolName.METADATA_FINDER.value:
                    is_metadata_retrieved = True
                # Add to thought chain
                if (
                    history_record["role"] == "assistant"
                    and "content" in agent_input
                    and agent_input["content"]
                    and agent_input["content"][0]
                    and "text" in agent_input["content"][0]
                ):
                    try:
                        thought_text_json = json.loads(agent_input["content"][0]["text"])
                        if "current_reasoning" in thought_text_json:
                            thought_chain.append(thought_text_json["current_reasoning"])
                    except Exception as e:
                        chat_log.warning(
                            f"Error in on_agent_invoked when adding to thought chain: {e}\n{traceback.format_exc()}"
                        )
        cycle_count += 1
        agent_inputs_list.append(
            {
                "role": "user",
                "cycle_index": cycle_count,
                "reference_id": "",
                "content": "User Input: \n" + json.dumps(input_data, indent=2) + "\n\n",
            }
        )

        # If this is not retry, then scan the uploaded files
        if try_count == 0 and not self.is_skip_attachment_scan:
            attachment_input = self.traverse_attachment_folder(conversation, chat_log)
            if attachment_input:
                agent_inputs_list.append(attachment_input)

        # Set the name for agent by conversation id
        self.name = "Complex Problem Solver - " + conversation.chat_id
        final_answer_list = []
        final_answer = ""
        self.update_followup_history(conversation, chat_log, {"agent_inputs": agent_inputs_list})

        # Use asyncio.run() to handle async calls within synchronous method
        is_completion_detected = False
        try:
            while True:
                # Check if user has stopped the conversation, if so break
                if conversation.active_session.user_stopped_stream:
                    chat_log.debug(
                        "iterative_problem_solver_agent | on_agent_invoked | User stopped stream, aborting the run loop...."
                    )
                    break

                result = asyncio.run(
                    self.run_iteration(
                        conversation,
                        cycle_count,
                        agent_inputs_list,
                        is_metadata_retrieved,
                        chat_log,
                    )
                )
                if result["result"] is not None:
                    chat_log.debug(f"Cycle {cycle_count} - Result:\n {result['result']}\n\n")
                    agent_inputs_list = result["next_input"]
                    if hasattr(result["result"], "current_reasoning") and result["result"].current_reasoning:
                        thought_chain.append(result["result"].current_reasoning)
                    # self.update_followup_history(conversation, chat_log, {"agent_inputs": agent_inputs_list})

                    is_metadata_retrieved = result["is_metadata_retrieved"]
                    if (
                        cycle_count == 1
                        and hasattr(result["result"], "analysis_title")
                        and result["result"].analysis_title
                    ):
                        conversation.is_conversation_title_required = True
                        self.save_and_stream_title(result["result"].analysis_title, conversation, chat_log)

                    # check if completed
                    if result["is_complete"] and not is_completion_detected:
                        is_completion_detected = True
                        chat_log.info(f"Analysis completed in {cycle_count} cycles. Verifying the answer....")
                        # Re-invoke one more cycle to double-check and verify the answer
                        # Reset the agent data

                        agent_inputs_list.append(
                            {
                                "role": "user",
                                "content": "Please review/cross verify whether the objective of the user is completely achieved and answer is accurate. \
                                If yes, then repeat the final answer. \
                                If correction needed or incomplete, mention it, re-run the process until user's objective is completely achieved.",
                            }
                        )
                        continue
                    elif result["is_user_input_required"] or (result["is_complete"] and is_completion_detected):
                        chat_log.info(
                            f"Analysis and verification completed in {cycle_count} cycles. Final answer is ready."
                        )
                        # Present the final answer
                        final_answer = result["result"].final_answer
                        final_answer_list = self.present_final_answer(conversation, final_answer, chat_log)
                        break
                else:
                    chat_log.warning(f"Cycle {cycle_count} - No output in the Result:\n {result}\n\n")
                    break
                cycle_count += 1

        except Exception as e:
            chat_log.error(f"Exception in on_agent_invoked: {e}\n{traceback.format_exc()}")
            raise e

        # self.save_agent_state(conversation, agent_inputs_list, chat_log)
        # Return the final result
        return self.on_success(
            input_data,
            {
                "result": str(result["result"]),
                "final_answer_list": final_answer_list,
                "final_answer": final_answer,
                "agent_inputs": agent_inputs_list,
                "thought_chain": thought_chain,
            },
        )

    # def save_agent_state(self, conversation: Conversation, current_input_data: dict, chat_log: Logger):
    #     # Save current input data to conversation folder for reference by followup questions
    #     conversation_path = f"storage/public/{conversation.chat_id}"
    #     if not os.path.exists(conversation_path):
    #         os.makedirs(conversation_path)
    #     with open(f"{conversation_path}/agent_input_data.json", "w") as f:
    #         json.dump(current_input_data, f)

    # def get_agent_state(self, conversation: Conversation, chat_log: Logger):
    #     conversation_path = f"storage/public/{conversation.chat_id}"
    #     if not os.path.exists(conversation_path):
    #         return []
    #     with open(f"{conversation_path}/agent_input_data.json", "r") as f:
    #         return json.load(f)

    def update_followup_history(self, conversation, chat_log, output_data):
        # # Save result to follow up history
        # self.add_to_history(
        #     conversation,
        #     "user",
        #     ["Previous User request: " + conversation.user_inputs[-1]],
        #     is_required_for_follow_ups=True,
        # )
        # if output_data.get("result", None) is not None:
        #     self.add_to_history(
        #         conversation,
        #         "assistant",
        #         ["Previous answer and reasoning:\n\n" + output_data["result"]],
        #         is_required_for_follow_ups=True,
        #     )
        # First flush the history
        self.clear_history(conversation, False)
        if output_data.get("agent_inputs", None) is not None:
            # Add each agent input to history
            for agent_input in output_data["agent_inputs"]:
                if "role" in agent_input:
                    self.add_to_history(
                        conversation,
                        agent_input["role"],
                        [json.dumps(agent_input)],
                        is_required_for_follow_ups=True,
                    )

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("IterativeProblemSolverAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
        return "Failed to solve the problem after multiple attempts."

    def push_stream_chunk(self, conversation: Conversation, chunk_message: str):
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=chunk_message,
            handler_type=DataBlockHandlerType.STREAM,
            block_tab_types=[FrontendTabContentType.ANSWER],
            user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
        )

    def get_data_file_previews(self, conversation: Conversation, user_question: str, chat_log):
        from utils.metalake import load_data

        summarize_input_list: list[tuple[str, bool]] = []
        data_file_previews = []
        for data_file_name in conversation.data_reference_set:
            data_file_path = f"storage/public/{conversation.chat_id}/{data_file_name}.pkl"
            if os.path.exists(data_file_path):
                data = load_data(conversation.chat_id, data_file_name)
                if not data.empty:
                    summarize_input_list.append(
                        (f"Data file name: {data_file_name}\n\n{data.head(5).to_markdown()}", True)
                    )
                    # data_file_previews.append(
                    #     {
                    #         "data_file_name": data_file_name,
                    #         "preview_data": data.head(5).to_markdown(),
                    #     }
                    # )
        data_file_previews = self.content_archive_tool_instance.summarize_results(
            conversation,
            AgentToolName.USER_INPUT,
            0,
            "List of available data files with first 5 rows of data for the question: " + user_question,
            summarize_input_list,
            "user",
            chat_log,
        )
        return data_file_previews

    def present_final_answer(self, conversation: Conversation, final_answer: str, chat_log):
        # Parse the final answer to get the structured content for both files and tables
        structured_content = self.parse_markdown_with_files(
            conversation, final_answer, chat_log, tag_names=["file", "table"]
        )

        # clear the session answer list
        conversation.active_session.session_answer_list.clear()

        for content in structured_content:
            try:
                if content["type"] == "markdown":
                    conversation.active_session.session_answer_list.append(
                        {
                            "content": content["content"],
                            "content_type": MessageContentType.TEXT.value,
                            "is_data_reviewer_verified": True,
                            "is_final_answer_processing_needed": False,
                            "is_final_answer_processing_done": True,
                        }
                    )
                elif content["type"] == "file":
                    # check file exists in storage/public/{conversation.chat_id}
                    file_path = f"storage/public/{conversation.chat_id}/files/{content['content']}"
                    if os.path.exists(file_path):
                        file_type = os.path.splitext(file_path)[1]

                        # determine file type is image or other
                        if file_type in [".png", ".jpg", ".jpeg", ".gif"]:
                            media_type = "image"
                        else:
                            media_type = file_type.replace(".", "")

                        # if file type is image, then get the image size
                        if media_type == "image":
                            with Image.open(file_path) as img:
                                width = img.width
                                height = img.height
                        else:
                            width = 0
                            height = 0

                        conversation.active_session.session_answer_list.append(
                            {
                                "content": [
                                    {
                                        "mediaUrl": f"{FRONTEND_URL}/api/file/public/{conversation.chat_id}/download?file_name={content['content']}",
                                        "fileKey": content["content"],
                                        "width": width,
                                        "height": height,
                                        "mediaType": media_type,
                                        "fileName": content["content"],
                                        "isMetaLakeRedirect": False,
                                    }
                                ],
                                "content_type": MessageContentType.MEDIA.value,
                                "is_data_reviewer_verified": True,
                                "is_final_answer_processing_needed": False,
                                "is_final_answer_processing_done": True,
                            }
                        )
                    else:
                        chat_log.error(
                            f"iterative_problem_solver_agent.present_final_answer | File {file_path} does not exist"
                        )
                        conversation.active_session.session_answer_list.append(
                            {
                                "content": f"{content['content']} does not exist",
                                "content_type": MessageContentType.TEXT.value,
                                "is_data_reviewer_verified": True,
                                "is_final_answer_processing_needed": False,
                                "is_final_answer_processing_done": True,
                            }
                        )
                        continue
            except Exception as e:
                chat_log.error(
                    f"iterative_problem_solver_agent.present_final_answer | Error: {traceback.format_exc()}"
                )
                continue

        # Add the final answer to the conversation history
        conversation.add_to_history(
            {"role": "assistant", "content": final_answer},
            False,
            True,
            is_required_for_follow_ups=False,
            llm_section_type=SectionType.ANSWER.value,
        )

        # Publish the answer
        conversation.publish_answer(is_final_answer=True)
        conversation.set_active_session_llm_status(ConversationLLMStatus.LLM_FINISHED)
        # conversation.stop_stream = True
        return conversation.active_session.session_answer_list

    def parse_markdown_with_files(self, conversation: Conversation, text, chat_log, tag_names=["file"]):
        result = []
        cursor = 0
        try:
            # Collect all matches for all tag types
            all_matches = []
            for tag in tag_names:
                pattern = re.compile(
                    rf'<{tag}[^>]*\btype\s*=\s*"([^"]+)"[^>]*\bname\s*=\s*"([^"]+)"[^>]*>(.*?)</{tag}>',
                    re.DOTALL | re.IGNORECASE,
                )
                for match in pattern.finditer(text):
                    all_matches.append(
                        {
                            "start": match.start(),
                            "end": match.end(),
                            "tag": tag,
                            "file_type": match.group(1),
                            "file_name": match.group(2),
                            "file_title": match.group(3),
                        }
                    )
            # Sort all matches by their position in the text
            all_matches.sort(key=lambda m: m["start"])
            for match in all_matches:
                start, end = match["start"], match["end"]
                tag_name = match["tag"]
                file_type = match["file_type"]
                file_name = match["file_name"]
                file_title = match["file_title"]
                # Add markdown block before this tag
                if start > cursor:
                    markdown_block = text[cursor:start]
                    if markdown_block.strip():
                        result.append({"type": "markdown", "content": markdown_block, "alt": ""})
                # Add file or table block
                if tag_name == "file":
                    result.append({"type": "file", "content": file_name, "alt": file_title.strip()})
                elif tag_name == "table":
                    file_path = f"storage/public/{conversation.chat_id}/files/{file_name}"
                    markdown_table, is_truncated, row_count = csv_to_markdown(file_path, 20)
                    result.append({"type": "markdown", "content": markdown_table, "alt": ""})
                    remaining_rows = row_count - 20
                    if remaining_rows > 0:
                        result.append(
                            {
                                "type": "markdown",
                                "content": f"\n\n*Note: Table truncated to first 20 rows. There are {remaining_rows} more rows in the table.*\n\n",
                                "alt": "",
                            }
                        )
                # You can add more tag types here if needed
                cursor = end
            # Add any remaining markdown after last tag
            if cursor < len(text):
                markdown_block = text[cursor:]
                if markdown_block.strip():
                    result.append({"type": "markdown", "content": markdown_block, "title": ""})
        except Exception as e:
            chat_log.error(
                f"iterative_problem_solver_agent.parse_markdown_with_files | Error: {traceback.format_exc()}"
            )
        return result

    def before_session_stream_complete(self, conversation: Conversation, chat_log):
        """
        Perform actions before the session stream is completed.

        Args:
            conversation (Conversation): The conversation object
            chat_log: Logger for chat interactions
        """

        # call super method --> this will complete last section in UI
        BaseAgent.before_session_stream_complete(self, conversation, chat_log)

    def traverse_attachment_folder(self, conversation: Conversation, chat_log):
        # Scans the attachment directory and return the file names with agent input
        input_file_folder_path = f"storage/public/{conversation.chat_id}/attachments"
        file_names = []
        if os.path.exists(input_file_folder_path):
            file_names = [f.name for f in os.scandir(input_file_folder_path) if f.is_file()]
        if not file_names:
            return None
        agent_input = {
            "role": "user",
            "cycle_index": 0,
            "reference_id": "",
            "content": f"List of attached documents: {file_names}",
        }
        conversation.data_reference_set.update(file_names)
        return agent_input
