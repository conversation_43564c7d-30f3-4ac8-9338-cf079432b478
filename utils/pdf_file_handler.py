"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
pdf_file_handler.py use for handle services related to pdf files

* @class pdf_file_handler
* @description This class use for handle services related to pdf files
* <AUTHOR>
"""

import os
import pdfplumber
import pandas as pd


def extract_text_and_tables_from_pdf(pdf_path):
    result = []

    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages, start=1):
            # Detect tables
            tables = page.find_tables(
                table_settings={
                    "vertical_strategy": "text",
                    "horizontal_strategy": "lines",
                    "intersection_x_tolerance": 5,
                    "intersection_y_tolerance": 5,
                }
            )
            table_bboxes = [t.bbox for t in tables]
            # Extract all words
            words = page.extract_words()
            # Mark each word as being in a table or not
            for word in words:
                word["in_table"] = any(
                    (word["x1"] > t_x0 and word["x0"] < t_x1 and word["bottom"] > t_top and word["top"] < t_bottom)
                    for t_x0, t_top, t_x1, t_bottom in table_bboxes
                )
            # Group into lines, separating table/non-table
            lines = {}
            for word in words:
                y = round(word["top"])
                if y not in lines:
                    lines[y] = {"words": [], "in_table": word["in_table"]}
                lines[y]["words"].append(word)
            # Now build segments for each region
            non_table_segments = []
            table_line_indices = set()
            for y, info in sorted(lines.items()):
                line_text = " ".join(w["text"] for w in sorted(info["words"], key=lambda w: w["x0"]))
                if not info["in_table"]:
                    non_table_segments.append(line_text)
                else:
                    table_line_indices.add(y)
            # Extract each table separately (using .extract())
            tables_data = []
            for t in tables:
                df = pd.DataFrame(t.extract())
                if not df.empty:
                    tables_data.append(df)
            # Store results per page
            result.append(
                {
                    "page_num": page_num,
                    "tables": tables_data,
                    "non_table_segments": non_table_segments,
                }
            )
    return result
